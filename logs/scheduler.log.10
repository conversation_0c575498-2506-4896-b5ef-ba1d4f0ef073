2025-05-21 10:18:44,469 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for massumin
2025-05-21 10:18:44,472 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for massumin
2025-05-21 10:18:44,475 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for massumin
2025-05-21 10:18:44,476 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for massumin
2025-05-21 10:18:44,477 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for massumin
2025-05-21 10:18:44,478 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for massumin
2025-05-21 10:18:44,480 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for massumin
2025-05-21 10:18:44,483 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for massumin
2025-05-21 10:18:44,489 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for massumin
2025-05-21 10:18:44,490 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for massumin
2025-05-21 10:18:44,492 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for massumin
2025-05-21 10:18:44,494 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for massumin
2025-05-21 10:18:44,499 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for massumin
2025-05-21 10:18:44,501 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for massumin
2025-05-21 10:18:44,502 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for massumin
2025-05-21 10:18:44,505 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for massumin
2025-05-21 10:18:44,509 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for massumin
2025-05-21 10:18:44,511 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for massumin
2025-05-21 10:18:44,513 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for massumin
2025-05-21 10:18:44,516 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for massumin
2025-05-21 10:19:45,057 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for massumin
2025-05-21 10:19:45,060 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for massumin
2025-05-21 10:19:45,062 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for massumin
2025-05-21 10:19:45,064 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for massumin
2025-05-21 10:19:45,066 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for massumin
2025-05-21 10:19:45,068 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for massumin
2025-05-21 10:19:45,070 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for massumin
2025-05-21 10:19:45,071 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for massumin
2025-05-21 10:19:45,074 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for massumin
2025-05-21 10:19:45,075 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for massumin
2025-05-21 10:19:45,078 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for massumin
2025-05-21 10:19:45,081 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for massumin
2025-05-21 10:19:45,083 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for massumin
2025-05-21 10:19:45,084 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for massumin
2025-05-21 10:19:45,085 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for massumin
2025-05-21 10:19:45,088 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for massumin
2025-05-21 10:19:45,090 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for massumin
2025-05-21 10:19:45,091 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for massumin
2025-05-21 10:19:45,093 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for massumin
2025-05-21 10:19:45,095 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for massumin
2025-05-21 10:19:45,097 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for massumin
2025-05-21 10:19:45,102 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for massumin
2025-05-21 10:19:45,103 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for massumin
2025-05-21 10:19:45,107 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for massumin
2025-05-21 10:19:45,109 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for massumin
2025-05-21 10:19:45,111 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for massumin
2025-05-21 10:19:45,112 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for massumin
2025-05-21 10:19:45,114 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for massumin
2025-05-21 10:19:45,120 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for massumin
2025-05-21 10:19:45,128 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for massumin
2025-05-21 10:19:45,130 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for massumin
2025-05-21 10:19:45,131 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for massumin
2025-05-21 10:19:45,133 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for massumin
2025-05-21 10:19:45,138 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for massumin
2025-05-21 10:19:45,140 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for massumin
2025-05-21 10:19:45,141 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for massumin
2025-05-21 10:19:45,142 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for massumin
2025-05-21 10:19:45,148 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for massumin
2025-05-21 10:19:45,150 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for massumin
2025-05-21 10:19:45,153 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for massumin
2025-05-21 10:19:45,156 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for massumin
2025-05-21 10:19:45,157 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for massumin
2025-05-21 10:19:45,159 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for massumin
2025-05-21 10:19:45,162 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for massumin
2025-05-21 10:19:45,165 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for massumin
2025-05-21 10:19:45,168 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for massumin
2025-05-21 10:19:45,171 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for massumin
2025-05-21 10:19:45,173 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for massumin
2025-05-21 10:20:45,485 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for massumin
2025-05-21 10:20:45,491 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for massumin
2025-05-21 10:20:45,499 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for massumin
2025-05-21 10:20:45,505 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for massumin
2025-05-21 10:20:45,513 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for massumin
2025-05-21 10:20:45,517 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for massumin
2025-05-21 10:20:45,519 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for massumin
2025-05-21 10:20:45,521 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for massumin
2025-05-21 10:20:45,523 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for massumin
2025-05-21 10:20:45,525 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for massumin
2025-05-21 10:20:45,529 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for massumin
2025-05-21 10:20:45,530 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for massumin
2025-05-21 10:20:45,532 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for massumin
2025-05-21 10:20:45,534 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for massumin
2025-05-21 10:20:45,536 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for massumin
2025-05-21 10:20:45,539 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for massumin
2025-05-21 10:20:45,544 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for massumin
2025-05-21 10:20:45,549 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for massumin
2025-05-21 10:20:45,555 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for massumin
2025-05-21 10:20:45,560 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for massumin
2025-05-21 10:20:45,563 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for massumin
2025-05-21 10:20:45,573 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for massumin
2025-05-21 10:20:45,575 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for massumin
2025-05-21 10:20:45,577 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for massumin
2025-05-21 10:20:45,579 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for massumin
2025-05-21 10:20:45,580 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for massumin
2025-05-21 10:20:45,583 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for massumin
2025-05-21 10:20:45,584 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for massumin
2025-05-21 10:20:45,586 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for massumin
2025-05-21 10:20:45,587 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for massumin
2025-05-21 10:20:45,589 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for massumin
2025-05-21 10:20:45,590 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for massumin
2025-05-21 10:20:45,592 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for massumin
2025-05-21 10:20:45,593 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for massumin
2025-05-21 10:20:45,596 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for massumin
2025-05-21 10:20:45,598 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for massumin
2025-05-21 10:20:45,602 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for massumin
2025-05-21 10:20:45,609 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for massumin
2025-05-21 10:20:45,610 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for massumin
2025-05-21 10:20:45,612 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for massumin
2025-05-21 10:20:45,616 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for massumin
2025-05-21 10:20:45,617 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for massumin
2025-05-21 10:20:45,618 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for massumin
2025-05-21 10:20:45,619 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for massumin
2025-05-21 10:20:45,620 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for massumin
2025-05-21 10:20:45,622 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for massumin
2025-05-21 10:20:45,623 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for massumin
2025-05-21 10:20:45,627 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for massumin
2025-05-21 10:21:46,364 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for massumin
2025-05-21 10:21:46,368 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for massumin
2025-05-21 10:21:46,370 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for massumin
2025-05-21 10:21:46,374 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for massumin
2025-05-21 10:21:46,375 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for massumin
2025-05-21 10:21:46,378 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for massumin
2025-05-21 10:21:46,380 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for massumin
2025-05-21 10:21:46,382 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for massumin
2025-05-21 10:21:46,393 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for massumin
2025-05-21 10:21:46,402 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for massumin
2025-05-21 10:21:46,407 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for massumin
2025-05-21 10:21:46,410 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for massumin
2025-05-21 10:21:46,411 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for massumin
2025-05-21 10:21:46,415 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for massumin
2025-05-21 10:21:46,418 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for massumin
2025-05-21 10:21:46,420 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for massumin
2025-05-21 10:21:46,422 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for massumin
2025-05-21 10:21:46,423 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for massumin
2025-05-21 10:21:46,424 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for massumin
2025-05-21 10:21:46,427 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for massumin
2025-05-21 10:21:46,433 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for massumin
2025-05-21 10:21:46,435 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for massumin
2025-05-21 10:21:46,443 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for massumin
2025-05-21 10:21:46,446 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for massumin
2025-05-21 10:21:46,449 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for massumin
2025-05-21 10:21:46,451 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for massumin
2025-05-21 10:21:46,452 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for massumin
2025-05-21 10:21:46,454 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for massumin
2025-05-21 10:21:46,455 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for massumin
2025-05-21 10:21:46,457 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for massumin
2025-05-21 10:21:46,460 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for massumin
2025-05-21 10:21:46,463 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for massumin
2025-05-21 10:21:46,464 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for massumin
2025-05-21 12:01:37,687 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rental
2025-05-21 12:01:37,691 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rental
2025-05-21 12:01:37,695 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rental
2025-05-21 12:01:37,700 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rental
2025-05-21 12:01:37,704 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rental
2025-05-21 12:01:37,705 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rental
2025-05-21 12:01:37,709 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rental
2025-05-21 12:01:37,713 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rental
2025-05-21 12:01:37,717 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rental
2025-05-21 12:01:37,718 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rental
2025-05-21 12:01:37,722 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rental
2025-05-21 12:01:37,725 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rental
2025-05-21 12:01:37,727 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rental
2025-05-21 12:01:37,728 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rental
2025-05-21 12:01:37,730 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rental
2025-05-21 12:01:37,736 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rental
2025-05-21 12:01:37,740 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rental
2025-05-21 12:01:37,746 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rental
2025-05-21 12:01:37,749 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rental
2025-05-21 12:01:37,752 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rental
2025-05-21 12:01:37,756 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rental
2025-05-21 12:01:37,761 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rental
2025-05-21 12:01:37,763 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rental
2025-05-21 12:01:37,772 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rental
2025-05-21 12:01:37,776 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rental
2025-05-21 12:01:37,783 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rental
2025-05-21 12:01:37,790 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rental
2025-05-21 12:01:37,793 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rental
2025-05-21 12:01:37,796 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rental
2025-05-21 12:01:37,797 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rental
2025-05-21 12:01:37,799 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rental
2025-05-21 12:01:37,811 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rental
2025-05-21 12:01:37,813 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rental
2025-05-21 12:01:37,832 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for explore
2025-05-21 12:01:37,835 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for explore
2025-05-21 12:01:37,841 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-05-21 12:01:37,845 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-05-21 12:01:37,849 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for explore
2025-05-21 12:01:37,853 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for explore
2025-05-21 12:01:37,855 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for explore
2025-05-21 12:01:37,859 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for explore
2025-05-21 12:01:37,862 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for explore
2025-05-21 12:01:37,875 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for explore
2025-05-21 12:01:37,876 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-05-21 12:01:37,877 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for explore
2025-05-21 12:01:37,880 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for explore
2025-05-21 12:01:37,890 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for explore
2025-05-21 12:01:37,892 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-05-21 12:01:37,894 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for explore
2025-05-21 12:01:37,896 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for explore
2025-05-21 12:01:37,903 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for explore
2025-05-21 12:01:37,906 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for explore
2025-05-21 12:01:37,910 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-05-21 12:01:37,912 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for explore
2025-05-21 12:01:37,913 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for explore
2025-05-21 12:01:37,916 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for explore
2025-05-21 12:01:37,921 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for explore
2025-05-21 12:01:37,923 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for explore
2025-05-21 12:01:37,927 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for explore
2025-05-21 12:01:37,931 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for explore
2025-05-21 12:01:37,932 ERROR scheduler Skipped queueing icd_tz.icd_tz.doctype.container.container.daily_update_date_container_stay because it was found in queue for explore
2025-05-21 12:01:37,945 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-05-21 12:01:37,968 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for massumin
2025-05-21 12:01:37,972 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for massumin
2025-05-21 12:01:37,977 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for massumin
2025-05-21 12:01:37,978 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for massumin
2025-05-21 12:01:37,981 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for massumin
2025-05-21 12:01:37,983 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for massumin
2025-05-21 12:01:37,987 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for massumin
2025-05-21 12:01:37,989 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for massumin
2025-05-21 12:01:37,992 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for massumin
2025-05-21 12:01:37,995 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for massumin
2025-05-21 12:01:38,000 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for massumin
2025-05-21 12:01:38,007 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for massumin
2025-05-21 12:01:38,009 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for massumin
2025-05-21 12:01:38,013 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for massumin
2025-05-21 12:01:38,018 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for massumin
2025-05-21 12:01:38,021 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for massumin
2025-05-21 12:01:38,023 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for massumin
2025-05-21 12:01:38,025 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for massumin
2025-05-21 12:01:38,028 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for massumin
2025-05-21 12:01:38,030 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for massumin
2025-05-21 12:01:38,036 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for massumin
2025-05-21 12:01:38,040 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for massumin
2025-05-21 12:01:38,051 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for massumin
2025-05-21 12:01:38,054 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for massumin
2025-05-21 12:01:38,058 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for massumin
2025-05-21 12:01:38,062 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for massumin
2025-05-21 12:01:38,067 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for massumin
2025-05-21 12:01:38,073 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for massumin
2025-05-21 12:01:38,075 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for massumin
2025-05-21 12:01:38,079 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for massumin
2025-05-21 12:01:38,082 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for massumin
2025-05-21 12:01:38,122 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for sdg
2025-05-21 12:01:38,155 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-05-21 12:01:38,169 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-05-21 12:01:38,189 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-05-21 12:01:38,191 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-05-21 12:01:38,219 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-05-21 12:01:38,268 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for sdg_esg
2025-05-21 12:01:38,275 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for sdg_esg
2025-05-21 12:01:38,277 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for sdg_esg
2025-05-21 12:01:38,283 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for sdg_esg
2025-05-21 12:01:38,285 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for sdg_esg
2025-05-21 12:01:38,292 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for sdg_esg
2025-05-21 12:01:38,294 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for sdg_esg
2025-05-21 12:01:38,309 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for sdg_esg
2025-05-21 12:01:38,315 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for sdg_esg
2025-05-21 12:01:38,320 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for sdg_esg
2025-05-21 12:01:38,328 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for sdg_esg
2025-05-21 12:01:38,351 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for sdg_esg
2025-05-21 12:01:38,362 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for sdg_esg
2025-05-21 12:01:38,434 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for education.test
2025-05-21 12:01:38,452 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for education.test
2025-05-21 12:01:38,457 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for education.test
2025-05-21 12:01:38,459 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for education.test
2025-05-21 12:01:38,460 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for education.test
2025-05-21 12:01:38,468 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for education.test
2025-05-21 12:01:38,469 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for education.test
2025-05-21 12:02:38,508 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for explore
2025-05-21 12:02:38,511 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-05-21 12:02:38,518 ERROR scheduler Skipped queueing icd_tz.icd_tz.doctype.container.container.daily_update_date_container_stay because it was found in queue for explore
2025-05-21 12:02:38,524 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for explore
2025-05-21 12:02:38,531 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for explore
2025-05-21 12:02:38,537 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for explore
2025-05-21 12:02:38,539 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for explore
2025-05-21 12:02:38,545 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for explore
2025-05-21 12:02:38,552 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-05-21 12:02:38,554 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for explore
2025-05-21 12:02:38,556 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for explore
2025-05-21 12:02:38,560 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for explore
2025-05-21 12:02:38,561 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for explore
2025-05-21 12:02:38,563 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-05-21 12:02:38,569 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for explore
2025-05-21 12:02:38,574 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for explore
2025-05-21 12:02:38,579 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for explore
2025-05-21 12:02:38,582 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-05-21 12:02:38,583 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for explore
2025-05-21 12:02:38,586 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for explore
2025-05-21 12:02:38,589 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-05-21 12:02:38,591 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for explore
2025-05-21 12:02:38,592 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for explore
2025-05-21 12:02:38,594 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for explore
2025-05-21 12:02:38,595 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for explore
2025-05-21 12:02:38,601 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for explore
2025-05-21 12:02:38,607 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-05-21 12:02:38,612 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for explore
2025-05-21 12:02:38,614 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for explore
2025-05-21 12:02:38,628 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rental
2025-05-21 12:02:38,630 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rental
2025-05-21 12:02:38,631 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rental
2025-05-21 12:02:38,633 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rental
2025-05-21 12:02:38,636 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rental
2025-05-21 12:02:38,647 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rental
2025-05-21 12:02:38,649 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rental
2025-05-21 12:02:38,651 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rental
2025-05-21 12:02:38,654 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rental
2025-05-21 12:02:38,655 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rental
2025-05-21 12:02:38,657 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rental
2025-05-21 12:02:38,660 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rental
2025-05-21 12:02:38,661 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rental
2025-05-21 12:02:38,665 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rental
2025-05-21 12:02:38,666 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rental
2025-05-21 12:02:38,668 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rental
2025-05-21 12:02:38,671 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rental
2025-05-21 12:02:38,672 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rental
2025-05-21 12:02:38,675 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rental
2025-05-21 12:02:38,677 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rental
2025-05-21 12:02:38,678 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rental
2025-05-21 12:02:38,681 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rental
2025-05-21 12:02:38,683 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rental
2025-05-21 12:02:38,684 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rental
2025-05-21 12:02:38,687 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rental
2025-05-21 12:02:38,688 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rental
2025-05-21 12:02:38,691 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rental
2025-05-21 12:02:38,692 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rental
2025-05-21 12:02:38,696 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rental
2025-05-21 12:02:38,704 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rental
2025-05-21 12:02:38,705 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rental
2025-05-21 12:02:38,720 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rental
2025-05-21 12:02:38,722 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rental
2025-05-21 12:02:38,769 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-05-21 12:02:38,796 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-05-21 12:02:38,826 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-05-21 12:02:38,827 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-05-21 12:02:38,837 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-05-21 12:02:38,858 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for education.test
2025-05-21 12:02:38,865 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for education.test
2025-05-21 12:02:38,867 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for education.test
2025-05-21 12:02:38,872 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for education.test
2025-05-21 12:02:38,879 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for education.test
2025-05-21 12:02:38,883 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for education.test
2025-05-21 12:02:38,890 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for education.test
2025-05-21 12:02:38,917 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for sdg_esg
2025-05-21 12:02:38,919 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for sdg_esg
2025-05-21 12:02:38,926 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for sdg_esg
2025-05-21 12:02:38,929 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for sdg_esg
2025-05-21 12:02:38,939 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for sdg_esg
2025-05-21 12:02:38,940 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for sdg_esg
2025-05-21 12:02:38,952 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for sdg_esg
2025-05-21 12:02:38,953 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for sdg_esg
2025-05-21 12:02:38,956 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for sdg_esg
2025-05-21 12:02:38,964 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for sdg_esg
2025-05-21 12:02:38,966 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for sdg_esg
2025-05-21 12:02:38,967 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for sdg_esg
2025-05-21 12:02:38,971 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for sdg_esg
2025-05-21 12:02:38,990 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for sdg
2025-05-21 12:02:39,037 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for massumin
2025-05-21 12:02:39,046 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for massumin
2025-05-21 12:02:39,049 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for massumin
2025-05-21 12:02:39,056 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for massumin
2025-05-21 12:02:39,059 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for massumin
2025-05-21 12:02:39,062 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for massumin
2025-05-21 12:02:39,068 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for massumin
2025-05-21 12:02:39,077 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for massumin
2025-05-21 12:02:39,080 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for massumin
2025-05-21 12:02:39,082 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for massumin
2025-05-21 12:02:39,085 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for massumin
2025-05-21 12:02:39,091 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for massumin
2025-05-21 12:02:39,093 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for massumin
2025-05-21 12:02:39,095 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for massumin
2025-05-21 12:02:39,096 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for massumin
2025-05-21 12:02:39,097 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for massumin
2025-05-21 12:02:39,100 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for massumin
2025-05-21 12:02:39,105 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for massumin
2025-05-21 12:02:39,106 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for massumin
2025-05-21 12:02:39,107 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for massumin
2025-05-21 12:02:39,109 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for massumin
2025-05-21 12:02:39,111 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for massumin
2025-05-21 12:02:39,112 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for massumin
2025-05-21 12:02:39,114 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for massumin
2025-05-21 12:02:39,119 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for massumin
2025-05-21 12:02:39,122 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for massumin
2025-05-21 12:02:39,124 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for massumin
2025-05-21 12:02:39,126 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for massumin
2025-05-21 12:02:39,128 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for massumin
2025-05-21 12:02:39,130 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for massumin
2025-05-21 12:02:39,134 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for massumin
2025-05-21 12:05:08,022 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for sdg_esg
2025-05-21 12:05:08,064 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for sdg_esg
2025-05-21 12:05:08,082 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for massumin
2025-05-21 12:05:08,123 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for massumin
2025-05-21 12:05:08,139 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for massumin
2025-05-21 12:05:08,141 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for massumin
2025-05-21 12:05:08,156 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for massumin
2025-05-21 12:05:08,294 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rental
2025-05-21 12:05:08,298 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rental
2025-05-21 12:05:08,305 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rental
2025-05-21 12:05:08,321 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rental
2025-05-21 12:05:08,322 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rental
2025-05-21 12:05:08,367 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for explore
2025-05-21 12:05:08,370 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for explore
2025-05-21 12:05:08,375 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-05-21 12:05:08,380 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for explore
2025-05-21 12:05:08,383 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-05-21 12:05:08,387 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for explore
2025-05-21 12:05:08,405 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for explore
2025-05-21 12:05:08,409 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for explore
2025-05-21 12:05:08,415 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-05-21 12:05:08,417 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for explore
2025-05-21 12:05:08,421 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for explore
2025-05-21 12:05:08,425 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for explore
2025-05-21 12:05:08,433 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-05-21 12:05:08,441 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-05-21 12:05:08,446 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for explore
2025-05-21 12:05:08,455 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for explore
2025-05-21 12:05:08,462 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-05-21 12:05:08,488 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-05-21 12:05:08,533 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-05-21 12:05:08,558 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-05-21 12:05:08,559 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-05-21 12:05:08,568 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-05-21 12:06:08,685 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for sdg_esg
2025-05-21 12:06:08,711 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for sdg_esg
2025-05-21 12:06:08,770 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-05-21 12:06:08,780 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-05-21 12:06:08,790 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-05-21 12:06:08,798 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-05-21 12:06:08,808 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-05-21 12:06:08,875 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-05-21 12:06:08,887 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for explore
2025-05-21 12:06:08,896 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-05-21 12:06:08,905 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for explore
2025-05-21 12:06:08,913 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-05-21 12:06:08,916 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-05-21 12:06:08,920 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for explore
2025-05-21 12:06:08,937 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for explore
2025-05-21 12:06:08,939 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-05-21 12:06:08,960 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for explore
2025-05-21 12:06:08,962 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for explore
2025-05-21 12:06:08,975 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for explore
2025-05-21 12:06:08,977 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for explore
2025-05-21 12:06:08,980 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for explore
2025-05-21 12:06:08,984 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for explore
2025-05-21 12:06:08,985 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for explore
2025-05-21 12:06:08,988 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-05-21 12:06:09,002 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for massumin
2025-05-21 12:06:09,040 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for massumin
2025-05-21 12:06:09,046 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for massumin
2025-05-21 12:06:09,064 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for massumin
2025-05-21 12:06:09,068 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for massumin
2025-05-21 12:06:09,130 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rental
2025-05-21 12:06:09,137 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rental
2025-05-21 12:06:09,157 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rental
2025-05-21 12:06:09,182 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rental
2025-05-21 12:06:09,206 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rental
2025-08-07 15:16:41,250 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:16:41,263 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:16:41,267 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:16:41,297 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:16:41,306 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:16:41,311 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:16:41,320 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:16:41,323 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:16:41,341 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:16:41,351 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:16:41,355 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:17:41,434 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-07 15:17:41,443 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-07 15:17:41,451 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-07 15:17:41,463 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-07 15:17:41,465 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-07 15:17:41,469 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-07 15:17:41,480 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-07 15:17:41,483 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-07 15:17:41,485 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-07 15:17:41,488 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-07 15:17:41,508 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-07 15:17:41,515 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-07 15:17:41,525 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-07 15:17:41,534 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-07 15:17:41,536 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-07 15:17:41,543 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-07 15:17:41,547 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-07 15:17:41,571 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rental
2025-08-07 15:17:41,574 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.reference.get_points_of_care because it was found in queue for rental
2025-08-07 15:17:41,576 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rental
2025-08-07 15:17:41,578 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rental
2025-08-07 15:17:41,580 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rental
2025-08-07 15:17:41,581 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for rental
2025-08-07 15:17:41,583 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for rental
2025-08-07 15:17:41,585 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.price_package.get_nhif_schemes because it was found in queue for rental
2025-08-07 15:17:41,586 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rental
2025-08-07 15:17:41,588 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.verification.get_visit_types because it was found in queue for rental
2025-08-07 15:17:41,589 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rental
2025-08-07 15:17:41,591 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for rental
2025-08-07 15:17:41,593 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for rental
2025-08-07 15:17:41,594 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for rental
2025-08-07 15:17:41,596 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for rental
2025-08-07 15:17:41,597 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rental
2025-08-07 15:17:41,599 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for rental
2025-08-07 15:17:41,601 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rental
2025-08-07 15:17:41,602 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for rental
2025-08-07 15:17:41,604 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for rental
2025-08-07 15:17:41,606 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.update_appointment_status because it was found in queue for rental
2025-08-07 15:17:41,607 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rental
2025-08-07 15:17:41,609 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_room_types because it was found in queue for rental
2025-08-07 15:17:41,610 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for rental
2025-08-07 15:17:41,612 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for rental
2025-08-07 15:17:41,614 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rental
2025-08-07 15:17:41,615 ERROR scheduler Skipped queueing non_profit.non_profit.doctype.membership.membership.set_expired_status because it was found in queue for rental
2025-08-07 15:17:41,617 ERROR scheduler Skipped queueing erpnext.accounts.deferred_revenue.process_deferred_accounting because it was found in queue for rental
2025-08-07 15:17:41,619 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rental
2025-08-07 15:17:41,621 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for rental
2025-08-07 15:17:41,622 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rental
2025-08-07 15:17:41,624 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rental
2025-08-07 15:17:41,625 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rental
2025-08-07 15:17:41,627 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rental
2025-08-07 15:17:41,629 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rental
2025-08-07 15:17:41,630 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for rental
2025-08-07 15:17:41,632 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for rental
2025-08-07 15:17:41,633 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rental
2025-08-07 15:17:41,635 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_z_report.vfd_z_report.make_vfd_z_report because it was found in queue for rental
2025-08-07 15:17:41,637 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rental
2025-08-07 15:17:41,638 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_admission_types because it was found in queue for rental
2025-08-07 15:17:41,640 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for rental
2025-08-07 15:17:41,642 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_ward_types because it was found in queue for rental
2025-08-07 15:17:41,643 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for rental
2025-08-07 15:17:41,645 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for rental
2025-08-07 15:17:41,646 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rental
2025-08-07 15:17:41,648 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rental
2025-08-07 15:17:41,649 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.send_overstay_nofication because it was found in queue for rental
2025-08-07 15:17:41,650 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for rental
2025-08-07 15:17:41,652 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for rental
2025-08-07 15:17:41,654 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rental
2025-08-07 15:17:41,655 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for rental
2025-08-07 15:17:41,657 ERROR scheduler Skipped queueing csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request because it was found in queue for rental
2025-08-07 15:17:41,658 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rental
2025-08-07 15:17:41,660 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for rental
2025-08-07 15:17:41,661 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rental
2025-08-07 15:17:41,663 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rental
2025-08-07 15:17:41,664 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rental
2025-08-07 15:17:41,666 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.tz_insurance_cover_note.tz_insurance_cover_note.update_covernote_docs because it was found in queue for rental
2025-08-07 15:17:41,667 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rental
2025-08-07 15:17:41,668 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rental
2025-08-07 15:17:41,670 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rental
2025-08-07 15:17:41,671 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for rental
2025-08-07 15:17:41,672 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rental
2025-08-07 15:17:41,674 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for rental
2025-08-07 15:17:41,676 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for rental
2025-08-07 15:17:41,677 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.facility.get_facilities because it was found in queue for rental
2025-08-07 15:17:41,679 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for rental
2025-08-07 15:17:41,680 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rental
2025-08-07 15:17:41,682 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for rental
2025-08-07 15:17:41,683 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_z_report.vfd_z_report.send_multi_vfd_z_reports because it was found in queue for rental
2025-08-07 15:17:41,685 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rental
2025-08-07 15:17:41,686 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for rental
2025-08-07 15:17:41,688 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rental
2025-08-07 15:17:41,690 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rental
2025-08-07 15:17:41,692 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rental
2025-08-07 15:17:41,693 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_submit_nhif_patient_claim because it was found in queue for rental
2025-08-07 15:17:41,695 ERROR scheduler Skipped queueing vfd_tz.api.utils.check_vfd_status because it was found in queue for rental
2025-08-07 15:17:41,696 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_monthly because it was found in queue for rental
2025-08-07 15:17:41,697 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rental
2025-08-07 15:17:41,698 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rental
2025-08-07 15:17:41,700 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rental
2025-08-07 15:17:41,701 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.approval.get_approval_services because it was found in queue for rental
2025-08-07 15:17:41,703 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_finalize_patient_encounters because it was found in queue for rental
2025-08-07 15:17:41,704 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rental
2025-08-07 15:17:41,706 ERROR scheduler Skipped queueing hms_tz.nhif.api.inpatient_record.daily_update_inpatient_occupancies because it was found in queue for rental
2025-08-07 15:17:41,707 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for rental
2025-08-07 15:17:41,709 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rental
2025-08-07 15:17:41,710 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for rental
2025-08-07 15:17:41,711 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rental
2025-08-07 15:17:41,713 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rental
2025-08-07 15:17:41,714 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for rental
2025-08-07 15:17:41,716 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rental
2025-08-07 15:17:41,717 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rental
2025-08-07 15:17:41,719 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rental
2025-08-07 15:17:41,720 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for rental
2025-08-07 15:17:41,722 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.price_package.get_item_types because it was found in queue for rental
2025-08-07 15:17:41,723 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for rental
2025-08-07 15:17:41,725 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rental
2025-08-07 15:17:41,726 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rental
2025-08-07 15:17:41,727 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for rental
2025-08-07 15:17:41,728 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for rental
2025-08-07 15:17:41,730 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for rental
2025-08-07 15:17:41,731 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for rental
2025-08-07 15:17:41,732 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for rental
2025-08-07 15:17:41,734 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for rental
2025-08-07 15:17:41,735 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for rental
2025-08-07 15:17:41,737 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rental
2025-08-07 15:17:41,738 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_tax_invoice.vfd_tax_invoice.posting_all_vfd_invoices_off_peak because it was found in queue for rental
2025-08-07 15:17:41,739 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for rental
2025-08-07 15:17:41,741 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rental
2025-08-07 15:17:41,742 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rental
2025-08-07 15:17:41,744 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for rental
2025-08-07 15:17:41,745 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for rental
2025-08-07 15:17:41,746 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for rental
2025-08-07 15:17:41,748 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rental
2025-08-07 15:17:41,749 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for rental
2025-08-07 15:17:41,750 ERROR scheduler Skipped queueing vfd_tz.vfd_tz.doctype.vfd_tax_invoice.vfd_tax_invoice.posting_all_vfd_invoices because it was found in queue for rental
2025-08-07 15:17:41,752 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for rental
2025-08-07 15:17:41,753 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for rental
2025-08-07 15:17:41,754 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for rental
2025-08-07 15:17:41,756 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rental
2025-08-07 15:17:41,757 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rental
2025-08-07 15:17:41,758 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_monthly because it was found in queue for rental
