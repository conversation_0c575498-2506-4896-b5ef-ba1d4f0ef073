{% from 'print_designer/page/print_designer/jinja/macros/render.html' import render with context %}
{% from 'print_designer/page/print_designer/jinja/macros/render_google_fonts.html' import render_google_fonts with context %}
{% from 'print_designer/page/print_designer/jinja/macros/styles.html' import render_styles with context %}
{% from 'print_designer/page/print_designer/jinja/macros/styles_old.html' import render_old_styles with context %}

{{ render_google_fonts(settings) }}

<!-- Don't remove this. user_generated_jinja_code tag is used as placeholder which we replace with user provided jinja template  -->
<!-- user_generated_jinja_code -->
<!-- end of user generated code -->

<div id="__print_designer">
    {% set header_available = pd_format.header.firstPage or pd_format.header.oddPage or pd_format.header.evenPage or pd_format.header.lastPage %}
    {%- if settings.page.headerHeight != 0 and header_available -%}
    <div id="header-html">
        <div style="position: relative; top:0px; left: 0px; width: 100%; height:{{ settings.page.headerHeightWithMargin }}px; overflow: hidden;" id="header-render-container">
            <div class="visible-pdf" style="height: {{ settings.page.marginTop }}px;"></div>
            <div class="hidden-pdf printview-header-margin" style="height: {{ settings.page.marginTop }}px;"></div>
            <div class="hidden-pdf">{% if pd_format.header.firstPage %}{{ render(pd_format.header.firstPage, send_to_jinja) }}{%endif%}</div>
            <div id="firstPageHeader" style="display: block;">{% if pd_format.header.firstPage %}{{ render(pd_format.header.firstPage, send_to_jinja) }}{%endif%}</div>
            <div id="oddPageHeader" style="display: none;">{% if pd_format.header.oddPage %}{{ render(pd_format.header.oddPage, send_to_jinja) }}{%endif%}</div>
            <div id="evenPageHeader" style="display: none;">{% if pd_format.header.evenPage %}{{ render(pd_format.header.evenPage, send_to_jinja) }}{%endif%}</div>
            <div id="lastPageHeader" style="display: none;">{% if pd_format.header.lastPage %}{{ render(pd_format.header.lastPage, send_to_jinja) }}{%endif%}</div>
        </div>
    </div>
    {%- endif -%}
    {%- for body in pd_format.body -%}
        {{ render(body.childrens, send_to_jinja) }}
    {%- endfor -%}
    {% set footer_available = pd_format.footer.firstPage or pd_format.footer.oddPage or pd_format.footer.evenPage or pd_format.footer.lastPage %}
    {%- if settings.page.footerHeight != 0 and footer_available -%}
    <div id="footer-html">
        <div style="width: 100%; position: relative; top:0px; left: 0px; height:{{ settings.page.footerHeightWithMargin }}px;" id="footer-render-container">
            <div class="hidden-pdf">{% if pd_format.footer.firstPage %}{{ render(pd_format.footer.firstPage, send_to_jinja) }}{%endif%}</div>
            <div class="visible-pdf" id="firstPageFooter" style="display: block;">{% if pd_format.footer.firstPage %}{{ render(pd_format.footer.firstPage, send_to_jinja) }}{%endif%}</div>
            <div class="visible-pdf" id="oddPageFooter" style="display: none;">{% if pd_format.footer.oddPage %}{{ render(pd_format.footer.oddPage, send_to_jinja) }}{%endif%}</div>
            <div class="visible-pdf" id="evenPageFooter" style="display: none;">{% if pd_format.footer.evenPage %}{{ render(pd_format.footer.evenPage, send_to_jinja) }}{%endif%}</div>
            <div class="visible-pdf" id="lastPageFooter" style="display: none;">{% if pd_format.footer.lastPage %}{{ render(pd_format.footer.lastPage, send_to_jinja) }}{%endif%}</div>
        </div>
    </div>
    {%- endif -%}
</div>

{%- if pdf_generator == "chrome" -%}
    {{ render_styles(settings) }}
{%- else -%}
    {{ render_old_styles(settings) }}
{%- endif -%}
