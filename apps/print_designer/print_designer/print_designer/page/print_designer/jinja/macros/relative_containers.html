{% from 'print_designer/page/print_designer/jinja/macros/render_element.html' import render_element with context %}

{% macro relative_columns(element, send_to_jinja) -%}
    {%- set heightType = element.get("heightType") -%}
    {%- if settings.get("schema_version") == "1.1.0" -%}
        {%- set heightType = "auto" if element.get("isDynamicHeight", False) else "fixed" -%}
    {%- endif -%}
    {%- set pageBreak = element.get("breakInside", "auto") -%}
    <div style="position:relative;  top: 0px; {%- if element.rectangleContainer -%}margin-top:{{element.startY}}px; margin-left:{{element.startX}}px;{%- endif -%} width:{{ element.width }}px; {%- if heightType != 'auto' -%}{%- if heightType == 'auto-min-height' -%}min-{%- endif -%}height:{{ element.height }}px; {%- endif -%}  break-inside: {{ pageBreak }}; {{convert_css(element.style)}}"
    class="rectangle {{ element.classes | join(' ') }}">
        {% if element.childrens %}
            {% for object in element.childrens %}
                {%- if object.layoutType == "row" -%}
                    {{ relative_containers(object, send_to_jinja) }}
                {%- elif object.layoutType == "column" -%}
                    {{ relative_columns(object, send_to_jinja) }}
                {%- else -%}
                    {{ render_element(object, send_to_jinja, heightType) }}
                {%- endif -%}
            {% endfor %}
        {% endif %}
    </div>
{%- endmacro %}

{% macro relative_containers(element, send_to_jinja) -%}
    {%- set heightType = element.get("heightType") -%}
    {%- if settings.get("schema_version") == "1.1.0" -%}
        {%- set heightType = "auto" if element.get("isDynamicHeight", False) else "fixed" -%}
    {%- endif -%}
    {%- set pageBreak = element.get("breakInside", "auto") -%}
    <div style="position:relative; left:{{ element.startX }}px; {%- if heightType != 'auto' -%}{%- if heightType == 'auto-min-height' -%}min-{%- endif -%}height:{{ element.height }}px; {%- endif -%} break-inside: {{ pageBreak }}; {{convert_css(element.style)}}"
    class="rectangle relative-row {{ element.classes | join(' ') }}">
        {% if element.childrens %}
            {% for object in element.childrens %}
                {%- if object.layoutType == "column" -%}
                    {{ relative_columns(object, send_to_jinja) }}
                {%- elif object.layoutType == "row" -%}
                    {{ relative_containers(object, send_to_jinja) }}
                {%- else -%}
                    {{ render_element(object, send_to_jinja, heightType) }}
                {%- endif -%}
            {% endfor %}
        {% endif %}
    </div>
{%- endmacro %}