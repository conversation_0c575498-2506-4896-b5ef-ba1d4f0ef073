import frappe


def execute():
    """
    Create Clearing Document Types with their attributes.
    Following the icd_tz pattern for master data creation.
    """
    for document_data in get_clearing_document_types():
        document_type = document_data.get("document_type")
        
        if not frappe.db.exists("Clearing Document Type", document_type):
            # Create the document
            doc = frappe.get_doc(document_data)
            doc.insert(ignore_permissions=True)
            frappe.db.commit()


def get_clearing_document_types():
    """
    Returns the list of Clearing Document Types to be created.
    This replaces the need for a fixtures folder.
    """
    return [
        {
            "doctype": "Clearing Document Type",
            "document_type": "Bill of Lading B/L",
            "linked_document": "Clearing File",
            "clearing_document_attribute": [
                {"document_attribute": "B/L Number ", "mandatory": 0},
                {"document_attribute": "MTD NO ", "mandatory": 0},
                {"document_attribute": "Shipment Ref No", "mandatory": 0},
                {"document_attribute": "Place of Acceptance", "mandatory": 0},
                {"document_attribute": "Port of Loading", "mandatory": 0},
                {"document_attribute": "Port of Discharge", "mandatory": 0},
                {"document_attribute": "Place of delivery", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Delivery Order",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Delivery Order Number", "mandatory": 0},
                {"document_attribute": "Order Date", "mandatory": 0},
                {"document_attribute": "Shipping Address/Delivery location", "mandatory": 0},
                {"document_attribute": "Order Reference", "mandatory": 0},
                {"document_attribute": "Tracking Number", "mandatory": 0},
                {"document_attribute": "Delivery Terms", "mandatory": 0},
                {"document_attribute": "Payment Terms", "mandatory": 0},
                {"document_attribute": "Item Description", "mandatory": 0},
                {"document_attribute": "Total Value", "mandatory": 0},
                {"document_attribute": "Delivery Instructions", "mandatory": 0},
                {"document_attribute": "Contact Information", "mandatory": 0},
                {"document_attribute": "Purchase Order Number", "mandatory": 0},
                {"document_attribute": "Sales Order Number", "mandatory": 0},
                {"document_attribute": "Invoice Number", "mandatory": 0},
                {"document_attribute": "Customs Information", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Tanzania Bureau Of Standards - Debit Advice",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Control Number", "mandatory": 0},
                {"document_attribute": "Currency", "mandatory": 0},
                {"document_attribute": "Debit No", "mandatory": 0},
                {"document_attribute": "Generated Date", "mandatory": 0},
                {"document_attribute": "Expiry Date", "mandatory": 0},
                {"document_attribute": "Service Fee ", "mandatory": 0},
                {"document_attribute": "Physical Verification Fee", "mandatory": 0},
                {"document_attribute": "Total", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Certificate of Origin",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Certificate Number", "mandatory": 0},
                {"document_attribute": " Date of Issue", "mandatory": 0},
                {"document_attribute": "Issuing Authority", "mandatory": 0},
                {"document_attribute": "Goods Country of Origin", "mandatory": 0},
                {"document_attribute": "Origin Criterion", "mandatory": 0},
                {"document_attribute": "Preferential or Non-Preferential", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Payment Note",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Tax Type - Import Duty (IMP)", "mandatory": 0},
                {"document_attribute": "Tax Type - Customs Processing Fee (CPF)", "mandatory": 0},
                {"document_attribute": "Tax Type - Railway Development Levy (RDL)", "mandatory": 0},
                {"document_attribute": "Tax Type - Value Added Tax (VAT)", "mandatory": 0},
                {"document_attribute": "Tax Type - Excise Duty", "mandatory": 0},
                {"document_attribute": "Notice Bill No", "mandatory": 0},
                {"document_attribute": "Notice Date", "mandatory": 0},
                {"document_attribute": "Notice Bill Type", "mandatory": 0},
                {"document_attribute": "Bill Tax Amount", "mandatory": 0},
                {"document_attribute": "Currency", "mandatory": 0},
                {"document_attribute": "Payment Control Number", "mandatory": 0},
                {"document_attribute": "Value Date", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Assessment Document",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Exporter Declaration Type", "mandatory": 0},
                {"document_attribute": "Processing/Clearing office", "mandatory": 0},
                {"document_attribute": "TANSAD Number & Date", "mandatory": 0},
                {"document_attribute": " CL. Plan", "mandatory": 0},
                {"document_attribute": "Consignee Freight Charges", "mandatory": 0},
                {"document_attribute": "Currency", "mandatory": 0},
                {"document_attribute": "Total Invoice Value ", "mandatory": 0},
                {"document_attribute": "Invoice No. & Date", "mandatory": 0},
                {"document_attribute": "Insurance", "mandatory": 0},
                {"document_attribute": "No Pckgs", "mandatory": 0},
                {"document_attribute": "Gross Weight", "mandatory": 0},
                {"document_attribute": "AWB/BL/RCN/Shipping Order", "mandatory": 0},
                {"document_attribute": "Clearing Agent Ref. No & Date", "mandatory": 0},
                {"document_attribute": "H.S. Code", "mandatory": 0},
                {"document_attribute": "Item (s) Description", "mandatory": 0},
                {"document_attribute": "IMP", "mandatory": 0},
                {"document_attribute": "CPF", "mandatory": 0},
                {"document_attribute": "RDL", "mandatory": 0},
                {"document_attribute": "VAT", "mandatory": 0},
                {"document_attribute": "Total Taxes for Item (s)", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Commercial Invoice",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Number of Commercial Invoices ", "mandatory": 0},
                {"document_attribute": "Commercial Reference No. (s)", "mandatory": 0},
                {"document_attribute": "Commercial Reference Date (s)", "mandatory": 0},
                {"document_attribute": "HSN (s)", "mandatory": 0},
                {"document_attribute": "Grand Total ", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Bank Document",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Beneficiary", "mandatory": 0},
                {"document_attribute": "Bank Name", "mandatory": 0},
                {"document_attribute": "Account Number", "mandatory": 0},
                {"document_attribute": "Bank Swift", "mandatory": 0},
                {"document_attribute": "Address", "mandatory": 0},
                {"document_attribute": "Amount To Pay", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Insurance Certificate",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Policy Number", "mandatory": 0},
                {"document_attribute": "Insurance Company", "mandatory": 0},
                {"document_attribute": "Policy Period ", "mandatory": 0},
                {"document_attribute": "Coverage Type", "mandatory": 0},
                {"document_attribute": "Sum Insured", "mandatory": 0},
                {"document_attribute": "Incoterms", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Air Waybill (AWB)",
            "linked_document": "Clearing File",
            "clearing_document_attribute": [
                {"document_attribute": "HS Code", "mandatory": 0},
                {"document_attribute": "Country of Origin", "mandatory": 0},
                {"document_attribute": "AWB Number", "mandatory": 0},
                {"document_attribute": "Goods Description ", "mandatory": 0},
                {"document_attribute": "other Reference numbers", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Packing List",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Shipment number or reference", "mandatory": 0},
                {"document_attribute": "Invoice number", "mandatory": 0},
                {"document_attribute": "Date of shipment", "mandatory": 0},
                {"document_attribute": "Package Number", "mandatory": 0},
                {"document_attribute": "Package Type", "mandatory": 0},
                {"document_attribute": "Dimensions", "mandatory": 0},
                {"document_attribute": "Gross Weight", "mandatory": 0},
                {"document_attribute": "Net Weight", "mandatory": 0},
                {"document_attribute": "Item Description", "mandatory": 0},
                {"document_attribute": "Quantity", "mandatory": 0},
                {"document_attribute": "Unit of Measure", "mandatory": 0},
                {"document_attribute": "HS Code", "mandatory": 0},
                {"document_attribute": "Country of Origin", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Import License",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "License Number", "mandatory": 0},
                {"document_attribute": "Issue Date", "mandatory": 0},
                {"document_attribute": "Expiry Date", "mandatory": 0},
                {"document_attribute": "Issuing Authority", "mandatory": 0},
                {"document_attribute": "Product Information", "mandatory": 0},
                {"document_attribute": "License Conditions", "mandatory": 0},
                {"document_attribute": "License Type", "mandatory": 0},
                {"document_attribute": "Country of Origin", "mandatory": 0},
                {"document_attribute": "Port of Entry", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Letter of Credit (LC)",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "LC Number", "mandatory": 0},
                {"document_attribute": "Issue Date", "mandatory": 0},
                {"document_attribute": "Amount", "mandatory": 0},
                {"document_attribute": "Currency", "mandatory": 0},
                {"document_attribute": "Description of Goods", "mandatory": 0},
                {"document_attribute": "Incoterms", "mandatory": 0},
                {"document_attribute": "Documents Required", "mandatory": 0},
                {"document_attribute": "Payment Terms", "mandatory": 0},
                {"document_attribute": "Expiry Date", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Import Declaration",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Declaration Type", "mandatory": 0},
                {"document_attribute": "Declaration Number", "mandatory": 0},
                {"document_attribute": "Date", "mandatory": 0},
                {"document_attribute": "Items", "mandatory": 0},
                {"document_attribute": "Invoice Number", "mandatory": 0},
                {"document_attribute": "Packing List Number", "mandatory": 0},
                {"document_attribute": "Goods Country of Origin", "mandatory": 0},
                {"document_attribute": "HS Code", "mandatory": 0},
                {"document_attribute": "Customs Value", "mandatory": 0},
                {"document_attribute": "Currency", "mandatory": 0},
                {"document_attribute": "Duties and Taxes", "mandatory": 0},
                {"document_attribute": "Licenses or Permits", "mandatory": 0},
                {"document_attribute": "Certificates", "mandatory": 0},
                {"document_attribute": "Incoterms", "mandatory": 0},
                {"document_attribute": "Payment Terms", "mandatory": 0},
                {"document_attribute": "Tariff Preferences", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Phytosanitary Certificate",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Certificate Number", "mandatory": 0},
                {"document_attribute": "Issue Date", "mandatory": 0},
                {"document_attribute": "Plant Product Description", "mandatory": 0},
                {"document_attribute": "phytosanitary Measures", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Health Certificate",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Certificate Number", "mandatory": 0},
                {"document_attribute": "Issue Date", "mandatory": 0},
                {"document_attribute": "Issuing Authority", "mandatory": 0},
                {"document_attribute": "Product Description", "mandatory": 0},
                {"document_attribute": "Health Status", "mandatory": 0},
                {"document_attribute": "Production and Processing Information", "mandatory": 0},
                {"document_attribute": "Laboratory Results", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Inspection Certificate",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Type of Inspection Certificate", "mandatory": 0},
                {"document_attribute": "Certificate Number", "mandatory": 0},
                {"document_attribute": "Issue Date", "mandatory": 0},
                {"document_attribute": "Issuing Authority", "mandatory": 0},
                {"document_attribute": "Product Description", "mandatory": 0},
                {"document_attribute": "Inspection Date(s)", "mandatory": 0},
                {"document_attribute": "Inspection Location", "mandatory": 0},
                {"document_attribute": "Inspection Scope", "mandatory": 0},
                {"document_attribute": "Inspection Findings", "mandatory": 0},
                {"document_attribute": "Standards or Specifications", "mandatory": 0},
            ]
        },
        {
            "doctype": "Clearing Document Type",
            "document_type": "Import Duty Payment Receipt",
            "linked_document": None,
            "clearing_document_attribute": [
                {"document_attribute": "Receipt Number", "mandatory": 0},
                {"document_attribute": "Issue Date", "mandatory": 0},
                {"document_attribute": "Payer Information", "mandatory": 0},
                {"document_attribute": "Customs Office", "mandatory": 0},
                {"document_attribute": "Declaration Number", "mandatory": 0},
                {"document_attribute": "Payment Details", "mandatory": 0},
                {"document_attribute": "Bank Details (if applicable)", "mandatory": 0},
                {"document_attribute": "Exchange Rate", "mandatory": 0},
                {"document_attribute": "Payment Date (different from issue date if applicable)", "mandatory": 0},
                {"document_attribute": "Payment Receipt Number", "mandatory": 0},
                {"document_attribute": "Overpayment/Refund Information", "mandatory": 0},
                {"document_attribute": "Payment Reference", "mandatory": 0},
            ]
        },
    ]
