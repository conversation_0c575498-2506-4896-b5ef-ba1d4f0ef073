/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const LucideBadgeHelp: typeof import('~icons/lucide/badge-help')['default']
  const LucideBook: typeof import('~icons/lucide/book')['default']
  const LucideCloudAlert: typeof import('~icons/lucide/cloud-alert')['default']
  const LucideCloudCheck: typeof import('~icons/lucide/cloud-check')['default']
  const LucideCloudSync: typeof import("~icons/lucide/cloud-sync")["default"]
  const LucideCloudUp: typeof import("~icons/lucide/cloud-up")["default"]
  const LucideCloudUpload: typeof import("~icons/lucide/cloud-upload")["default"]
  const LucideFileText: typeof import('~icons/lucide/file-text')['default']
  const LucideFolderSync: typeof import('~icons/lucide/folder-sync')['default']
  const LucideMoon: typeof import('~icons/lucide/moon')['default']
  const LucideT: typeof import('~icons/lucide/t')['default']
  const LucideTr: typeof import('~icons/lucide/tr')['default']
  const LucideTrash: typeof import('~icons/lucide/trash')['default']
  const LucideUser: typeof import('~icons/lucide/user')['default']
}
