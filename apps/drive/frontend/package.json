{"name": "frontend", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "debug": "vite build --mode=development --base=/assets/drive/frontend/ && yarn copy-html-entry", "build": "vite build --base=/assets/drive/frontend/ && yarn copy-html-entry", "copy-html-entry": "cp ../drive/public/frontend/index.html ../drive/www/drive.html", "preview": "vite preview", "precommit": "pretty-quick --staged", "lint": "eslint --fix --ext .js,.vue ."}, "dependencies": {"@headlessui-float/vue": "^0.13.3", "@headlessui/vue": "^1.7.23", "@sereneinserenade/tiptap-comment-extension": "^0.1.2", "@tiptap/extension-font-family": "^2.14.0", "@tiptap/pm": "^2.7.0", "@vueuse/components": "^13.1.0", "@vueuse/core": "^10.3.0", "date-fns": "^4.1.0", "dropzone": "^6.0.0-beta.2", "frappe-ui": "0.1.185", "html2pdf.js": "^0.10.3", "idb-keyval": "^6.2.1", "mammoth": "^1.6.0", "mitt": "^3.0.0", "reka-ui": "^2.4.1", "sass": "^1.62.0", "slugify": "^1.6.6", "socket.io-client": "^4.7.5", "uuid": "^9.0.0", "vue": "^3.5.14", "vue-router": "^4.1.6", "vue-tippy": "^6.7.1", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "^9.9.0", "husky": "^8.0.3", "less": "^4.2.0", "postcss": "^8.4.21", "prettier": "^2.8.4", "pretty-quick": "^3.1.3", "tailwindcss": "^3.4.17", "vite": "^4.5.14", "vite-plugin-top-level-await": "^1.4.1"}}