/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccessButton: typeof import('./src/components/ShareDialog/AccessButton.vue')['default']
    ActivityTree: typeof import('./src/components/ActivityTree.vue')['default']
    ActivityTreeItem: typeof import('./src/components/ActivityTreeItem.vue')['default']
    ActivityTreeShare: typeof import('./src/components/ActivityTreeShare.vue')['default']
    AppsIcon: typeof import('./src/components/AppsIcon.vue')['default']
    AppSwitcher: typeof import('./src/components/AppSwitcher.vue')['default']
    Archive: typeof import('./src/components/MimeIcons/Archive.vue')['default']
    Audio: typeof import('./src/components/MimeIcons/Audio.vue')['default']
    AudioPreview: typeof import('./src/components/FileTypePreview/AudioPreview.vue')['default']
    BottomBar: typeof import('./src/components/BottomBar.vue')['default']
    ColorPicker: typeof import('./src/components/DocEditor/components/ColorPicker.vue')['default']
    CommentEditor: typeof import('./src/components/DocEditor/components/CommentEditor.vue')['default']
    ConfirmDialog: typeof import('./src/components/ConfirmDialog.vue')['default']
    ContextMenu: typeof import('./src/components/ContextMenu.vue')['default']
    CTADeleteDialog: typeof import('./src/components/CTADeleteDialog.vue')['default']
    CustomListRow: typeof import('./src/components/CustomListRow.vue')['default']
    CustomListRowItem: typeof import('./src/components/CustomListRowItem.vue')['default']
    DeleteDialog: typeof import('./src/components/DeleteDialog.vue')['default']
    Dialogs: typeof import('./src/components/Dialogs.vue')['default']
    Document: typeof import('./src/components/MimeIcons/Document.vue')['default']
    DriveToolBar: typeof import('./src/components/DriveToolBar.vue')['default']
    EditTagDialog: typeof import('./src/components/Settings/EditTagDialog.vue')['default']
    ErrorPage: typeof import('./src/components/ErrorPage.vue')['default']
    FilePicker: typeof import('./src/components/FilePicker.vue')['default']
    FileRender: typeof import('./src/components/FileRender.vue')['default']
    FileUploader: typeof import('./src/components/FileUploader.vue')['default']
    FloatingComments: typeof import('./src/components/DocEditor/components/FloatingComments.vue')['default']
    Folder: typeof import('./src/components/MimeIcons/Folder.vue')['default']
    FontFamily: typeof import('./src/components/DocEditor/components/FontFamily.vue')['default']
    FrappeDriveLogo: typeof import('./src/components/FrappeDriveLogo.vue')['default']
    GeneralAccess: typeof import('./src/components/GeneralAccess.vue')['default']
    GeneralDialog: typeof import('./src/components/ConfirmDialog.vue')['default']
    GenericDialog: typeof import('./src/components/GenericDialog.vue')['default']
    GenericPage: typeof import('./src/components/GenericPage.vue')['default']
    GridItem: typeof import('./src/components/GridItem.vue')['default']
    GridView: typeof import('./src/components/GridView.vue')['default']
    H1: typeof import('./src/components/DocEditor/icons/h-1.vue')['default']
    H2: typeof import('./src/components/DocEditor/icons/h-2.vue')['default']
    H3: typeof import('./src/components/DocEditor/icons/h-3.vue')['default']
    Image: typeof import('./src/components/MimeIcons/Image.vue')['default']
    ImagePreview: typeof import('./src/components/FileTypePreview/ImagePreview.vue')['default']
    InfoPopup: typeof import('./src/components/InfoPopup.vue')['default']
    InfoSidebar: typeof import('./src/components/InfoSidebar.vue')['default']
    ListView: typeof import('./src/components/ListView.vue')['default']
    LucideAlertCircle: typeof import('~icons/lucide/alert-circle')['default']
    LucideArrowBigLeft: typeof import('~icons/lucide/arrow-big-left')['default']
    LucideArrowDownAz: typeof import('~icons/lucide/arrow-down-az')['default']
    LucideArrowRight: typeof import('~icons/lucide/arrow-right')['default']
    LucideArrowUpZa: typeof import('~icons/lucide/arrow-up-za')['default']
    LucideAxe: typeof import('~icons/lucide/axe')['default']
    LucideBadgeCheck: typeof import('~icons/lucide/badge-check')['default']
    LucideBuilding: typeof import('~icons/lucide/building')['default']
    LucideCheck: typeof import('~icons/lucide/check')['default']
    LucideChevronDown: typeof import('~icons/lucide/chevron-down')['default']
    LucideChevronRight: typeof import('~icons/lucide/chevron-right')['default']
    LucideCircle: typeof import('~icons/lucide/circle')['default']
    LucideClock: typeof import('~icons/lucide/clock')['default']
    LucideEllipsis: typeof import('~icons/lucide/ellipsis')['default']
    LucideEyeOff: typeof import('~icons/lucide/eye-off')['default']
    LucideFile: typeof import('~icons/lucide/file')['default']
    LucideFolderClosed: typeof import('~icons/lucide/folder-closed')['default']
    LucideFolderOpenDot: typeof import('~icons/lucide/folder-open-dot')['default']
    LucideFolderPlus: typeof import('~icons/lucide/folder-plus')['default']
    LucideGlobe2: typeof import('~icons/lucide/globe2')['default']
    LucideHome: typeof import('~icons/lucide/home')['default']
    LucideInfo: typeof import('~icons/lucide/info')['default']
    LucideLink2: typeof import('~icons/lucide/link2')['default']
    LucideLock: typeof import('~icons/lucide/lock')['default']
    LucideLogOut: typeof import('~icons/lucide/log-out')['default']
    LucideMessageCircleCode: typeof import('~icons/lucide/message-circle-code')['default']
    LucideMessageCircleReply: typeof import('~icons/lucide/message-circle-reply')['default']
    LucideMinus: typeof import('~icons/lucide/minus')['default']
    LucideMoreHorizontal: typeof import('~icons/lucide/more-horizontal')['default']
    LucideMoreVertical: typeof import('~icons/lucide/more-vertical')['default']
    LucideSearch: typeof import('~icons/lucide/search')['default']
    LucideSettings: typeof import('~icons/lucide/settings')['default']
    LucideStar: typeof import('~icons/lucide/star')['default']
    LucideTag: typeof import('~icons/lucide/tag')['default']
    LucideTrash: typeof import('~icons/lucide/trash')['default']
    LucideUser: typeof import('~icons/lucide/user')['default']
    LucideX: typeof import('~icons/lucide/x')['default']
    MoveDialog: typeof import('./src/components/MoveDialog.vue')['default']
    MSOfficePreview: typeof import('./src/components/FileTypePreview/MSOfficePreview.vue')['default']
    Navbar: typeof import('./src/components/Navbar.vue')['default']
    NewFolderDialog: typeof import('./src/components/NewFolderDialog.vue')['default']
    NewLinkDialog: typeof import('./src/components/NewLinkDialog.vue')['default']
    NewTagDialog: typeof import('./src/components/Settings/NewTagDialog.vue')['default']
    NoFilesSection: typeof import('./src/components/NoFilesSection.vue')['default']
    PDF: typeof import('./src/components/MimeIcons/PDF.vue')['default']
    PDFPreview: typeof import('./src/components/FileTypePreview/PDFPreview.vue')['default']
    Presentation: typeof import('./src/components/MimeIcons/Presentation.vue')['default']
    PrimaryDropdown: typeof import('./src/components/PrimaryDropdown.vue')['default']
    ProfileSettings: typeof import('./src/components/Settings/ProfileSettings.vue')['default']
    ProgressRing: typeof import('./src/components/ProgressRing.vue')['default']
    RemoveRestoreDialog: typeof import('./src/components/RemoveRestoreDialog.vue')['default']
    RenameDialog: typeof import('./src/components/RenameDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchPopup: typeof import('./src/components/SearchPopup.vue')['default']
    SettingsDialog: typeof import('./src/components/Settings/SettingsDialog.vue')['default']
    ShareDialog: typeof import('./src/components/ShareDialog/ShareDialog.vue')['default']
    ShortcutsDialog: typeof import('./src/components/ShortcutsDialog.vue')['default']
    Sidebar: typeof import('./src/components/Sidebar.vue')['default']
    SidebarItem: typeof import('./src/components/SidebarItem.vue')['default']
    Spreadsheet: typeof import('./src/components/MimeIcons/Spreadsheet.vue')['default']
    StorageBar: typeof import('./src/components/StorageBar.vue')['default']
    StorageSettings: typeof import('./src/components/Settings/StorageSettings.vue')['default']
    Tag: typeof import('./src/components/Tag.vue')['default']
    TagColorInput: typeof import('./src/components/TagColorInput.vue')['default']
    TagInput: typeof import('./src/components/TagInput.vue')['default']
    TagInputDeprecated: typeof import('./src/components/TagInputDeprecated.vue')['default']
    TagSettings: typeof import('./src/components/Settings/TagSettings.vue')['default']
    TeamSwitcher: typeof import('./src/components/TeamSwitcher.vue')['default']
    TextEditor: typeof import('./src/components/DocEditor/TextEditor.vue')['default']
    TextPreview: typeof import('./src/components/FileTypePreview/TextPreview.vue')['default']
    Toast: typeof import('./src/components/Toast.vue')['default']
    Unknown: typeof import('./src/components/MimeIcons/Unknown.vue')['default']
    UploadTracker: typeof import('./src/components/UploadTracker.vue')['default']
    UserAutoComplete: typeof import('./src/components/ShareDialog/UserAutoComplete.vue')['default']
    UserListSettings: typeof import('./src/components/Settings/UserListSettings.vue')['default']
    UsersBar: typeof import('./src/components/UsersBar.vue')['default']
    Video: typeof import('./src/components/MimeIcons/Video.vue')['default']
    VideoPreview: typeof import('./src/components/FileTypePreview/VideoPreview.vue')['default']
  }
}
