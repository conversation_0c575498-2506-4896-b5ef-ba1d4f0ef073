{"name": "drive", "lockfileVersion": 3, "requires": true, "packages": {"": {"hasInstallScript": true}, "frappe-ui": {"version": "0.1.183", "extraneous": true, "license": "MIT", "dependencies": {"@floating-ui/vue": "^1.1.6", "@headlessui/vue": "^1.7.14", "@popperjs/core": "^2.11.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.16", "@tiptap/core": "^2.26.1", "@tiptap/extension-code-block": "^2.26.1", "@tiptap/extension-code-block-lowlight": "^2.26.1", "@tiptap/extension-color": "^2.26.1", "@tiptap/extension-heading": "^2.26.1", "@tiptap/extension-highlight": "^2.26.1", "@tiptap/extension-image": "^2.26.1", "@tiptap/extension-link": "^2.26.1", "@tiptap/extension-mention": "^2.26.1", "@tiptap/extension-placeholder": "^2.26.1", "@tiptap/extension-table": "^2.26.1", "@tiptap/extension-table-cell": "^2.26.1", "@tiptap/extension-table-header": "^2.26.1", "@tiptap/extension-table-row": "^2.26.1", "@tiptap/extension-task-item": "^2.26.1", "@tiptap/extension-task-list": "^2.26.1", "@tiptap/extension-text-align": "^2.26.1", "@tiptap/extension-text-style": "^2.26.1", "@tiptap/extension-typography": "^2.26.1", "@tiptap/pm": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "@tiptap/suggestion": "^2.26.1", "@tiptap/vue-3": "^2.26.1", "@vueuse/core": "^10.4.1", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "echarts": "^5.6.0", "feather-icons": "^4.28.0", "grid-layout-plus": "^1.1.0", "highlight.js": "^11.11.1", "idb-keyval": "^6.2.0", "lowlight": "^3.3.0", "lucide-static": "^0.535.0", "marked": "^15.0.12", "ora": "5.4.1", "prettier": "^3.3.2", "radix-vue": "^1.5.3", "reka-ui": "^2.0.2", "socket.io-client": "^4.5.1", "tippy.js": "^6.3.7", "typescript": "^5.0.2", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.4.1"}, "devDependencies": {"@histoire/plugin-vue": "^0.17.17", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "histoire": "^0.17.17", "lint-staged": ">=10", "msw": "^2.7.0", "postcss": "^8.4.21", "prettier-plugin-tailwindcss": "^0.1.13", "tailwindcss": "^3.2.7", "vite": "^5.1.8", "vitest": "^2.1.8", "vue": "^3.3.0", "vue-router": "^4.1.6"}, "peerDependencies": {"vue": ">=3.5.0", "vue-router": "^4.1.6"}}, "frontend": {"version": "0.0.0", "extraneous": true, "dependencies": {"@headlessui-float/vue": "^0.13.3", "@headlessui/vue": "^1.7.23", "@sereneinserenade/tiptap-comment-extension": "^0.1.2", "@tiptap/extension-font-family": "^2.14.0", "@tiptap/pm": "^2.7.0", "@vueuse/components": "^13.1.0", "@vueuse/core": "^10.3.0", "date-fns": "^4.1.0", "dropzone": "^6.0.0-beta.2", "frappe-ui": "^0.1.183", "html2pdf.js": "^0.10.3", "idb-keyval": "^6.2.1", "mammoth": "^1.6.0", "mitt": "^3.0.0", "sass": "^1.62.0", "slugify": "^1.6.6", "socket.io-client": "^4.7.5", "uuid": "^9.0.0", "vue": "^3.5.14", "vue-router": "^4.1.6", "vue-tippy": "^6.7.1", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "^9.9.0", "husky": "^8.0.3", "less": "^4.2.0", "postcss": "^8.4.21", "prettier": "^2.8.4", "pretty-quick": "^3.1.3", "tailwindcss": "^3.4.17", "vite": "^4.5.14", "vite-plugin-top-level-await": "^1.4.1"}}}}