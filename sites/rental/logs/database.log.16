2025-06-02 11:04:17,404 WARNING database DDL Query made to DB:
create table `tabStock Reservation Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`has_serial_no` int(1) not null default 0,
`has_batch_no` int(1) not null default 0,
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_detail_no` varchar(140),
`from_voucher_type` varchar(140),
`from_voucher_no` varchar(140),
`from_voucher_detail_no` varchar(140),
`stock_uom` varchar(140),
`available_qty` decimal(21,9) not null default 0,
`voucher_qty` decimal(21,9) not null default 0,
`reserved_qty` decimal(21,9) not null default 0,
`delivered_qty` decimal(21,9) not null default 0,
`reservation_based_on` varchar(140) default 'Qty',
`company` varchar(140),
`project` varchar(140),
`status` varchar(140) default 'Draft',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item_code`(`item_code`),
index `warehouse`(`warehouse`),
index `voucher_no`(`voucher_no`),
index `voucher_detail_no`(`voucher_detail_no`),
index `from_voucher_no`(`from_voucher_no`),
index `company`(`company`),
index `project`(`project`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:17,501 WARNING database DDL Query made to DB:
create table `tabItem Barcode` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`barcode` varchar(140) unique,
`barcode_type` varchar(140),
`uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:17,585 WARNING database DDL Query made to DB:
create table `tabLanded Cost Taxes and Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense_account` varchar(140),
`account_currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 0,
`description` text,
`amount` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`has_corrective_cost` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:17,672 WARNING database DDL Query made to DB:
create table `tabCustoms Tariff Number` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tariff_number` varchar(140) unique,
`description` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:17,778 WARNING database DDL Query made to DB:
create table `tabStock Reconciliation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`purpose` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`set_warehouse` varchar(140),
`scan_barcode` varchar(140),
`scan_mode` int(1) not null default 0,
`expense_account` varchar(140),
`difference_amount` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:17,873 WARNING database DDL Query made to DB:
create table `tabQuality Inspection Parameter Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`group_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,047 WARNING database DDL Query made to DB:
create table `tabMaterial Request Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`schedule_date` date,
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`from_warehouse` varchar(140),
`warehouse` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`min_order_qty` decimal(21,9) not null default 0,
`projected_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`ordered_qty` decimal(21,9) not null default 0,
`received_qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`expense_account` varchar(140),
`wip_composite_asset` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`bom_no` varchar(140),
`project` varchar(140),
`cost_center` varchar(140),
`lead_time_date` date,
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`production_plan` varchar(140),
`material_request_plan_item` varchar(140),
`job_card_item` varchar(140),
`page_break` int(1) not null default 0,
index `item_code`(`item_code`),
index `item_name`(`item_name`),
index `item_group`(`item_group`),
index `sales_order_item`(`sales_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_index`(item_code, warehouse)
2025-06-02 11:04:18,182 WARNING database DDL Query made to DB:
create table `tabDelivery Stop` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`customer` varchar(140),
`address` varchar(140),
`locked` int(1) not null default 0,
`customer_address` text,
`visited` int(1) not null default 0,
`delivery_note` varchar(140),
`grand_total` decimal(21,9) not null default 0,
`contact` varchar(140),
`email_sent_to` varchar(140),
`customer_contact` text,
`distance` decimal(21,9) not null default 0,
`estimated_arrival` datetime(6),
`lat` decimal(21,9) not null default 0,
`uom` varchar(140),
`lng` decimal(21,9) not null default 0,
`details` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,256 WARNING database DDL Query made to DB:
create table `tabShipment Parcel` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`length` int(11) not null default 0,
`width` int(11) not null default 0,
`height` int(11) not null default 0,
`weight` decimal(21,9) not null default 0,
`count` int(11) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,332 WARNING database DDL Query made to DB:
create table `tabItem Quality Inspection Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`specification` varchar(140),
`parameter_group` varchar(140),
`value` varchar(140),
`numeric` int(1) not null default 1,
`min_value` decimal(21,9) not null default 0,
`max_value` decimal(21,9) not null default 0,
`formula_based_criteria` int(1) not null default 0,
`acceptance_formula` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,410 WARNING database DDL Query made to DB:
create table `tabItem Tax` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_tax_template` varchar(140),
`tax_category` varchar(140),
`valid_from` date,
`minimum_net_rate` decimal(21,9) not null default 0,
`maximum_net_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,482 WARNING database DDL Query made to DB:
create table `tabUOM Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,572 WARNING database DDL Query made to DB:
create table `tabItem Website Specification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,656 WARNING database DDL Query made to DB:
create table `tabUOM Conversion Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,770 WARNING database DDL Query made to DB:
create table `tabPrice List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`price_list_name` varchar(140) unique,
`currency` varchar(140),
`buying` int(1) not null default 0,
`selling` int(1) not null default 0,
`price_not_uom_dependent` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:18,910 WARNING database DDL Query made to DB:
create table `tabLanded Cost Voucher` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`posting_date` date,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`distribute_charges_based_on` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:19,018 WARNING database DDL Query made to DB:
create table `tabLanded Cost Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`description` longtext,
`receipt_document_type` varchar(140),
`receipt_document` varchar(140),
`qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`is_fixed_asset` int(1) not null default 0,
`applicable_charges` decimal(21,9) not null default 0,
`purchase_receipt_item` varchar(140),
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:19,136 WARNING database DDL Query made to DB:
create table `tabPick List Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` text,
`item_group` varchar(140),
`warehouse` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`stock_qty` decimal(21,9) not null default 0,
`picked_qty` decimal(21,9) not null default 0,
`stock_reserved_qty` decimal(21,9) not null default 0,
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`serial_no` text,
`batch_no` varchar(140),
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`product_bundle_item` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
index `item_code`(`item_code`),
index `serial_and_batch_bundle`(`serial_and_batch_bundle`),
index `batch_no`(`batch_no`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:19,276 WARNING database DDL Query made to DB:
create table `tabItem Variant` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_attribute` varchar(140),
`item_attribute_value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:19,425 WARNING database DDL Query made to DB:
create table `tabMaterial Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140) default '{material_request_type}',
`material_request_type` varchar(140),
`customer` varchar(140),
`company` varchar(140),
`transaction_date` date,
`schedule_date` date,
`amended_from` varchar(140),
`scan_barcode` varchar(140),
`set_from_warehouse` varchar(140),
`set_warehouse` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`status` varchar(140),
`per_ordered` decimal(21,9) not null default 0,
`transfer_status` varchar(140),
`per_received` decimal(21,9) not null default 0,
`letter_head` varchar(140),
`select_print_heading` varchar(140),
`job_card` varchar(140),
`work_order` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:19,612 WARNING database DDL Query made to DB:
create table `tabDelivery Trip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`email_notification_sent` int(1) not null default 0,
`driver` varchar(140),
`driver_name` varchar(140),
`driver_email` varchar(140),
`driver_address` varchar(140),
`total_distance` decimal(21,9) not null default 0,
`uom` varchar(140),
`vehicle` varchar(140),
`departure_time` datetime(6),
`employee` varchar(140),
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:20,526 WARNING database DDL Query made to DB:
create table `tabIssue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`subject` varchar(140),
`customer` varchar(140),
`raised_by` varchar(140),
`status` varchar(140) default 'Open',
`priority` varchar(140),
`issue_type` varchar(140),
`issue_split_from` varchar(140),
`description` longtext,
`service_level_agreement` varchar(140),
`response_by` datetime(6),
`agreement_status` varchar(140) default 'First Response Due',
`sla_resolution_by` datetime(6),
`service_level_agreement_creation` datetime(6),
`on_hold_since` datetime(6),
`total_hold_time` decimal(21,9),
`first_response_time` decimal(21,9),
`first_responded_on` datetime(6),
`avg_response_time` decimal(21,9),
`resolution_details` longtext,
`opening_date` date,
`opening_time` time(6),
`sla_resolution_date` datetime(6),
`resolution_time` decimal(21,9),
`user_resolution_time` decimal(21,9),
`lead` varchar(140),
`contact` varchar(140),
`email_account` varchar(140),
`customer_name` varchar(140),
`project` varchar(140),
`company` varchar(140),
`via_customer_portal` int(1) not null default 0,
`attachment` text,
`content_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer`(`customer`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:20,695 WARNING database DDL Query made to DB:
create table `tabPause SLA On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:20,782 WARNING database DDL Query made to DB:
create table `tabIssue Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:20,871 WARNING database DDL Query made to DB:
create table `tabIssue Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:20,995 WARNING database DDL Query made to DB:
create table `tabService Level Agreement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`default_priority` varchar(140),
`service_level` varchar(140),
`enabled` int(1) not null default 1,
`default_service_level_agreement` int(1) not null default 0,
`entity_type` varchar(140),
`entity` varchar(140),
`condition` longtext,
`start_date` date,
`end_date` date,
`apply_sla_for_resolution` int(1) not null default 1,
`holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:21,091 WARNING database DDL Query made to DB:
create table `tabSLA Fulfilled On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:21,178 WARNING database DDL Query made to DB:
create table `tabService Level Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_priority` int(1) not null default 0,
`priority` varchar(140),
`response_time` decimal(21,9),
`resolution_time` decimal(21,9),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:21,254 WARNING database DDL Query made to DB:
create table `tabService Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workday` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:21,427 WARNING database DDL Query made to DB:
create table `tabWarranty Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`status` varchar(140) default 'Open',
`complaint_date` date,
`customer` varchar(140),
`serial_no` varchar(140),
`complaint` longtext,
`item_code` varchar(140),
`item_name` varchar(140),
`description` text,
`warranty_amc_status` varchar(140),
`warranty_expiry_date` date,
`amc_expiry_date` date,
`resolution_date` datetime(6),
`resolved_by` varchar(140),
`resolution_details` text,
`customer_name` varchar(140),
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`territory` varchar(140),
`customer_group` varchar(140),
`customer_address` varchar(140),
`address_display` text,
`service_address` text,
`company` varchar(140),
`complaint_raised_by` varchar(140),
`from_company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index `complaint_date`(`complaint_date`),
index `customer`(`customer`),
index `serial_no`(`serial_no`),
index `item_code`(`item_code`),
index `warranty_amc_status`(`warranty_amc_status`),
index `resolution_date`(`resolution_date`),
index `resolved_by`(`resolved_by`),
index `territory`(`territory`),
index `company`(`company`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:21,538 WARNING database DDL Query made to DB:
create table `tabSupport Search Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140),
`source_type` varchar(140),
`base_url` varchar(140),
`query_route` varchar(140),
`search_term_param_name` varchar(140),
`response_result_key_path` varchar(140),
`post_route` varchar(140),
`post_route_key_list` varchar(140),
`post_title_key` varchar(140),
`post_description_key` varchar(140),
`source_doctype` varchar(140),
`result_title_field` varchar(140),
`result_preview_field` varchar(140),
`result_route_field` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:21,712 WARNING database DDL Query made to DB:
create table `tabVideo` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`provider` varchar(140),
`url` varchar(140),
`youtube_video_id` varchar(140),
`publish_date` date,
`duration` decimal(21,9),
`like_count` decimal(21,9) not null default 0,
`view_count` decimal(21,9) not null default 0,
`dislike_count` decimal(21,9) not null default 0,
`comment_count` decimal(21,9) not null default 0,
`description` longtext,
`image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:21,871 WARNING database DDL Query made to DB:
create table `tabPortal User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
index `user`(`user`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:22,013 WARNING database DDL Query made to DB:
create table `tabSMS Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sender_name` varchar(140),
`sent_on` date,
`message` text,
`no_of_requested_sms` int(11) not null default 0,
`requested_numbers` longtext,
`no_of_sent_sms` int(11) not null default 0,
`sent_to` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:22,192 WARNING database DDL Query made to DB:
create table `tabAsset Shift Allocation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`naming_series` varchar(140),
`finance_book` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:22,278 WARNING database DDL Query made to DB:
create table `tabMaintenance Team Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`team_member` varchar(140),
`full_name` varchar(140),
`maintenance_role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:22,389 WARNING database DDL Query made to DB:
create table `tabLocation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`parent_location` varchar(140),
`is_container` int(1) not null default 0,
`is_group` int(1) not null default 0,
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`area` decimal(21,9) not null default 0,
`area_uom` varchar(140),
`location` longtext,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `parent_location`(`parent_location`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:22,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabLocation`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-06-02 11:04:22,538 WARNING database DDL Query made to DB:
create table `tabAsset Category Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140),
`fixed_asset_account` varchar(140),
`accumulated_depreciation_account` varchar(140),
`depreciation_expense_account` varchar(140),
`capital_work_in_progress_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:22,637 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`maintenance_task` varchar(140),
`maintenance_type` varchar(140),
`maintenance_status` varchar(140),
`start_date` date,
`periodicity` varchar(140),
`end_date` date,
`certificate_required` int(1) not null default 0,
`assign_to` varchar(140),
`assign_to_name` varchar(140),
`next_due_date` date,
`last_completion_date` date,
`description` longtext,
index `certificate_required`(`certificate_required`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:22,758 WARNING database DDL Query made to DB:
create table `tabAsset Repair` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`company` varchar(140),
`asset_name` varchar(140),
`naming_series` varchar(140),
`failure_date` datetime(6),
`repair_status` varchar(140) default 'Pending',
`completion_date` datetime(6),
`cost_center` varchar(140),
`project` varchar(140),
`purchase_invoice` varchar(140),
`capitalize_repair_cost` int(1) not null default 0,
`stock_consumption` int(1) not null default 0,
`repair_cost` decimal(21,9) not null default 0,
`total_repair_cost` decimal(21,9) not null default 0,
`increase_in_asset_life` int(11) not null default 0,
`description` longtext,
`actions_performed` longtext,
`downtime` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:22,897 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`capitalization_method` varchar(140),
`target_item_code` varchar(140),
`target_item_name` varchar(140),
`target_asset` varchar(140),
`target_asset_name` varchar(140),
`target_qty` decimal(21,9) not null default 1.0,
`target_asset_location` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`finance_book` varchar(140),
`target_batch_no` varchar(140),
`target_serial_no` text,
`amended_from` varchar(140),
`target_is_fixed_asset` int(1) not null default 0,
`target_has_batch_no` int(1) not null default 0,
`target_has_serial_no` int(1) not null default 0,
`stock_items_total` decimal(21,9) not null default 0,
`asset_items_total` decimal(21,9) not null default 0,
`service_items_total` decimal(21,9) not null default 0,
`total_value` decimal(21,9) not null default 0,
`target_incoming_rate` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`target_fixed_asset_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `posting_date`(`posting_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:23,011 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Team` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`maintenance_team_name` varchar(140) unique,
`maintenance_manager` varchar(140),
`maintenance_manager_name` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:23,136 WARNING database DDL Query made to DB:
create table `tabAsset Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset_category_name` varchar(140) unique,
`enable_cwip_accounting` int(1) not null default 0,
`non_depreciable_category` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:23,238 WARNING database DDL Query made to DB:
create table `tabAsset Value Adjustment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`asset` varchar(140),
`asset_category` varchar(140),
`date` date,
`finance_book` varchar(140),
`amended_from` varchar(140),
`current_asset_value` decimal(21,9) not null default 0,
`new_asset_value` decimal(21,9) not null default 0,
`difference_amount` decimal(21,9) not null default 0,
`difference_account` varchar(140),
`journal_entry` varchar(140),
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:23,348 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Stock Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`warehouse` varchar(140),
`purchase_receipt_item` varchar(140),
`stock_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`serial_no` text,
`batch_no` varchar(140),
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:23,519 WARNING database DDL Query made to DB:
create table `tabAsset` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`asset_owner` varchar(140),
`asset_owner_company` varchar(140),
`is_existing_asset` int(1) not null default 0,
`is_composite_asset` int(1) not null default 0,
`supplier` varchar(140),
`customer` varchar(140),
`image` text,
`journal_entry_for_scrap` varchar(140),
`naming_series` varchar(140),
`asset_name` varchar(140),
`asset_category` varchar(140),
`location` varchar(140),
`split_from` varchar(140),
`custodian` varchar(140),
`department` varchar(140),
`disposal_date` date,
`cost_center` varchar(140),
`purchase_receipt` varchar(140),
`purchase_receipt_item` varchar(140),
`purchase_invoice` varchar(140),
`purchase_invoice_item` varchar(140),
`purchase_date` date,
`available_for_use_date` date,
`gross_purchase_amount` decimal(21,9) not null default 0,
`asset_quantity` int(11) not null default 1,
`additional_asset_cost` decimal(21,9) not null default 0,
`total_asset_cost` decimal(21,9) not null default 0,
`calculate_depreciation` int(1) not null default 0,
`opening_accumulated_depreciation` decimal(21,9) not null default 0,
`opening_number_of_booked_depreciations` int(11) not null default 0,
`is_fully_depreciated` int(1) not null default 0,
`depreciation_method` varchar(140),
`value_after_depreciation` decimal(21,9) not null default 0,
`total_number_of_depreciations` int(11) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`next_depreciation_date` date,
`policy_number` varchar(140),
`insurer` varchar(140),
`insured_value` varchar(140),
`insurance_start_date` date,
`insurance_end_date` date,
`comprehensive_insurance` varchar(140),
`maintenance_required` int(1) not null default 0,
`status` varchar(140) default 'Draft',
`booked_fixed_asset` int(1) not null default 0,
`purchase_amount` decimal(21,9) not null default 0,
`default_finance_book` varchar(140),
`depr_entry_posting_status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:23,625 WARNING database DDL Query made to DB:
create table `tabAsset Repair Consumed Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`valuation_rate` decimal(21,9) not null default 0,
`consumed_quantity` varchar(140),
`total_value` decimal(21,9) not null default 0,
`serial_no` text,
`serial_and_batch_bundle` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:23,738 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset_name` varchar(140) unique,
`asset_category` varchar(140),
`company` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`maintenance_team` varchar(140),
`maintenance_manager` varchar(140),
`maintenance_manager_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:23,828 WARNING database DDL Query made to DB:
create table `tabAsset Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`date` datetime(6),
`user` varchar(140),
`subject` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:23,920 WARNING database DDL Query made to DB:
create table `tabAsset Movement Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`asset` varchar(140),
`source_location` varchar(140),
`from_employee` varchar(140),
`asset_name` varchar(140),
`target_location` varchar(140),
`to_employee` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,001 WARNING database DDL Query made to DB:
create table `tabAsset Finance Book` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`finance_book` varchar(140),
`depreciation_method` varchar(140),
`total_number_of_depreciations` int(11) not null default 0,
`total_number_of_booked_depreciations` int(11) not null default 0,
`daily_prorata_based` int(1) not null default 0,
`shift_based` int(1) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`depreciation_start_date` date,
`salvage_value_percentage` decimal(21,9) not null default 0,
`expected_value_after_useful_life` decimal(21,9) not null default 0,
`value_after_depreciation` decimal(21,9) not null default 0,
`rate_of_depreciation` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,085 WARNING database DDL Query made to DB:
create table `tabDepreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`schedule_date` date,
`depreciation_amount` decimal(21,9) not null default 0,
`accumulated_depreciation_amount` decimal(21,9) not null default 0,
`journal_entry` varchar(140),
`shift` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,185 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset_maintenance` varchar(140),
`naming_series` varchar(140),
`asset_name` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`task` varchar(140),
`task_name` varchar(140),
`maintenance_type` varchar(140),
`periodicity` varchar(140),
`has_certificate` int(1) not null default 0,
`certificate_attachement` text,
`maintenance_status` varchar(140),
`assign_to_name` varchar(140),
`task_assignee_email` varchar(140),
`due_date` date,
`completion_date` date,
`description` varchar(140),
`actions_performed` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,282 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`expense_account` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,377 WARNING database DDL Query made to DB:
create table `tabAsset Depreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`naming_series` varchar(140),
`company` varchar(140),
`gross_purchase_amount` decimal(21,9) not null default 0,
`opening_accumulated_depreciation` decimal(21,9) not null default 0,
`opening_number_of_booked_depreciations` int(11) not null default 0,
`finance_book` varchar(140),
`finance_book_id` int(11) not null default 0,
`depreciation_method` varchar(140),
`total_number_of_depreciations` int(11) not null default 0,
`rate_of_depreciation` decimal(21,9) not null default 0,
`daily_prorata_based` int(1) not null default 0,
`shift_based` int(1) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`expected_value_after_useful_life` decimal(21,9) not null default 0,
`notes` text,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,457 WARNING database DDL Query made to DB:
create table `tabLinked Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,556 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Asset Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`asset_name` varchar(140),
`finance_book` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`current_asset_value` decimal(21,9) not null default 0,
`asset_value` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`fixed_asset_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,643 WARNING database DDL Query made to DB:
create table `tabAsset Movement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`purpose` varchar(140),
`transaction_date` datetime(6),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,736 WARNING database DDL Query made to DB:
create table `tabAsset Shift Factor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_name` varchar(140) unique,
`shift_factor` decimal(21,9) not null default 0,
`default` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:24,936 WARNING database DDL Query made to DB:
create table `tabWebsite Filter Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,005 WARNING database DDL Query made to DB:
create table `tabWebsite Attribute` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attribute` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,108 WARNING database DDL Query made to DB:
create table `tabHomepage Section Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`subtitle` varchar(140),
`image` text,
`content` text,
`route` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,233 WARNING database DDL Query made to DB:
create table `tabHomepage Section` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`section_based_on` varchar(140),
`no_of_columns` varchar(140) default '3',
`section_html` longtext,
`section_order` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,324 WARNING database DDL Query made to DB:
create table `tabMaintenance Schedule Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`scheduled_date` date,
`actual_date` date,
`sales_person` varchar(140),
`completion_status` varchar(140) default 'Pending',
`serial_no` text,
`item_reference` varchar(140),
index `item_code`(`item_code`),
index `scheduled_date`(`scheduled_date`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,425 WARNING database DDL Query made to DB:
create table `tabMaintenance Schedule Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`start_date` date,
`end_date` date,
`periodicity` varchar(140),
`no_of_visits` int(11) not null default 0,
`sales_person` varchar(140),
`serial_no` text,
`sales_order` varchar(140),
`serial_and_batch_bundle` varchar(140),
index `item_code`(`item_code`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `sales_order`(`sales_order`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,524 WARNING database DDL Query made to DB:
create table `tabMaintenance Visit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`address_display` text,
`contact_display` text,
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`maintenance_schedule` varchar(140),
`maintenance_schedule_detail` varchar(140),
`mntc_date` date,
`mntc_time` time(6),
`completion_status` varchar(140),
`maintenance_type` varchar(140) default 'Unscheduled',
`customer_feedback` text,
`status` varchar(140) default 'Draft',
`amended_from` varchar(140),
`company` varchar(140),
`customer_address` varchar(140),
`contact_person` varchar(140),
`territory` varchar(140),
`customer_group` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,625 WARNING database DDL Query made to DB:
create table `tabMaintenance Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`status` varchar(140) default 'Draft',
`transaction_date` date,
`customer_name` varchar(140),
`contact_person` varchar(140),
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`contact_display` text,
`customer_address` varchar(140),
`address_display` text,
`territory` varchar(140),
`customer_group` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `customer`(`customer`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,708 WARNING database DDL Query made to DB:
create table `tabMaintenance Visit Purpose` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`service_person` varchar(140),
`serial_no` varchar(140),
`description` longtext,
`work_done` text,
`prevdoc_doctype` varchar(140),
`prevdoc_docname` varchar(140),
`maintenance_schedule_detail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,810 WARNING database DDL Query made to DB:
create table `tabLower Deduction Certificate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tax_withholding_category` varchar(140),
`fiscal_year` varchar(140),
`company` varchar(140),
`certificate_no` varchar(140) unique,
`supplier` varchar(140),
`pan_no` varchar(140),
`valid_from` date,
`valid_upto` date,
`rate` decimal(21,9) not null default 0,
`certificate_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,904 WARNING database DDL Query made to DB:
create table `tabSouth Africa VAT Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:25,988 WARNING database DDL Query made to DB:
create table `tabUAE VAT Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:26,081 WARNING database DDL Query made to DB:
create table `tabImport Supplier Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invoice_series` varchar(140),
`company` varchar(140),
`item_code` varchar(140),
`supplier_group` varchar(140),
`tax_account` varchar(140),
`default_buying_price_list` varchar(140),
`zip_file` text,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:26,157 WARNING database DDL Query made to DB:
create table `tabUAE VAT Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:26,439 WARNING database DDL Query made to DB:
create table `tabQuality Procedure Process` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`process_description` longtext,
`procedure` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:26,535 WARNING database DDL Query made to DB:
create table `tabQuality Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`goal` varchar(140) unique,
`frequency` varchar(140) default 'None',
`procedure` varchar(140),
`weekday` varchar(140),
`date` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:26,627 WARNING database DDL Query made to DB:
create table `tabQuality Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`corrective_preventive` varchar(140) default 'Corrective',
`review` varchar(140),
`feedback` varchar(140),
`status` varchar(140) default 'Open',
`date` date,
`goal` varchar(140),
`procedure` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:26,714 WARNING database DDL Query made to DB:
create table `tabNon Conformance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`procedure` varchar(140),
`process_owner` varchar(140),
`full_name` varchar(140),
`status` varchar(140),
`details` longtext,
`corrective_action` longtext,
`preventive_action` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:26,800 WARNING database DDL Query made to DB:
create table `tabQuality Review` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`goal` varchar(140),
`date` date,
`procedure` varchar(140),
`status` varchar(140) default 'Open',
`additional_information` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:26,889 WARNING database DDL Query made to DB:
create table `tabQuality Review Objective` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` text,
`target` varchar(140),
`uom` varchar(140),
`status` varchar(140) default 'Open',
`review` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:26,963 WARNING database DDL Query made to DB:
create table `tabQuality Feedback Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parameter` varchar(140),
`rating` varchar(140) default '1',
`feedback` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,036 WARNING database DDL Query made to DB:
create table `tabQuality Goal Objective` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` text,
`target` varchar(140),
`uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,118 WARNING database DDL Query made to DB:
create table `tabQuality Feedback Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,194 WARNING database DDL Query made to DB:
create table `tabQuality Action Resolution` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`problem` longtext,
`resolution` longtext,
`status` varchar(140),
`responsible` varchar(140),
`completion_by` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,261 WARNING database DDL Query made to DB:
create table `tabQuality Feedback Template Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parameter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,330 WARNING database DDL Query made to DB:
create table `tabQuality Meeting Agenda` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`agenda` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,407 WARNING database DDL Query made to DB:
create table `tabQuality Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140),
`document_type` varchar(140),
`document_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,491 WARNING database DDL Query made to DB:
create table `tabQuality Meeting Minutes` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`minute` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,569 WARNING database DDL Query made to DB:
create table `tabQuality Meeting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Open',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,666 WARNING database DDL Query made to DB:
create table `tabQuality Procedure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`quality_procedure_name` varchar(140) unique,
`process_owner` varchar(140),
`process_owner_full_name` varchar(140),
`parent_quality_procedure` varchar(140),
`is_group` int(1) not null default 0,
`rgt` int(11) not null default 0,
`lft` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,773 WARNING database DDL Query made to DB:
create table `tabCommunication Medium` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`communication_channel` varchar(140),
`communication_medium_type` varchar(140),
`catch_all` varchar(140),
`provider` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,852 WARNING database DDL Query made to DB:
create table `tabCommunication Medium Timeslot` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6),
`to_time` time(6),
`employee_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:27,933 WARNING database DDL Query made to DB:
create table `tabTelephony Call Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`call_type` varchar(140) unique,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:28,013 WARNING database DDL Query made to DB:
create table `tabIncoming Call Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`call_routing` varchar(140) default 'Sequential',
`greeting_message` varchar(140),
`agent_busy_message` varchar(140),
`agent_unavailable_message` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:28,111 WARNING database DDL Query made to DB:
create table `tabVoice Call Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140) unique,
`call_receiving_device` varchar(140) default 'Computer',
`greeting_message` varchar(140),
`agent_busy_message` varchar(140),
`agent_unavailable_message` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:28,187 WARNING database DDL Query made to DB:
create table `tabIncoming Call Handling Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6) default '9:00:00',
`to_time` time(6) default '17:00:00',
`agent_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:28,287 WARNING database DDL Query made to DB:
create table `tabCall Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`id` varchar(140) unique,
`from` varchar(140),
`to` varchar(140),
`call_received_by` varchar(140),
`employee_user_id` varchar(140),
`medium` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`type` varchar(140),
`customer` varchar(140),
`status` varchar(140),
`duration` decimal(21,9),
`recording_url` varchar(140),
`type_of_call` varchar(140),
`summary` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:28,452 WARNING database DDL Query made to DB:
create table `tabBulk Transaction Log Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_doctype` varchar(140),
`transaction_name` varchar(140),
`date` date,
`time` time(6),
`transaction_status` varchar(140),
`error_description` longtext,
`to_doctype` varchar(140),
`retried` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `from_doctype`(`from_doctype`),
index `transaction_name`(`transaction_name`),
index `date`(`date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:28,589 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`bom` varchar(140),
`include_exploded_items` int(1) not null default 0,
`schedule_date` date,
`expected_delivery_date` date,
`description` longtext,
`image` text,
`qty` decimal(21,9) not null default 1.0,
`received_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`rm_cost_per_qty` decimal(21,9) not null default 0,
`service_cost_per_qty` decimal(21,9) not null default 0,
`additional_cost_per_qty` decimal(21,9) not null default 0,
`warehouse` varchar(140),
`expense_account` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`purchase_order_item` varchar(140),
`page_break` int(1) not null default 0,
`subcontracting_conversion_factor` decimal(21,9) not null default 0,
`job_card` varchar(140),
index `item_code`(`item_code`),
index `expected_delivery_date`(`expected_delivery_date`),
index `material_request`(`material_request`),
index `material_request_item`(`material_request_item`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:28,688 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`fg_item` varchar(140),
`fg_item_qty` decimal(21,9) not null default 1.0,
`purchase_order_item` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
index `item_code`(`item_code`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:28,785 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt Supplied Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`item_name` varchar(140),
`bom_detail_no` varchar(140),
`description` longtext,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`available_qty_for_consumption` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`current_stock` decimal(21,9) not null default 0,
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`subcontracting_order` varchar(140),
`serial_no` text,
`batch_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:28,934 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`is_scrap_item` int(1) not null default 0,
`description` longtext,
`brand` varchar(140),
`image` text,
`received_qty` decimal(21,9) not null default 0,
`qty` decimal(21,9) not null default 0,
`rejected_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`rm_cost_per_qty` decimal(21,9) not null default 0,
`service_cost_per_qty` decimal(21,9) not null default 0,
`additional_cost_per_qty` decimal(21,9) not null default 0,
`scrap_cost_per_qty` decimal(21,9) not null default 0,
`rm_supp_cost` decimal(21,9) not null default 0,
`warehouse` varchar(140),
`subcontracting_order` varchar(140),
`subcontracting_order_item` varchar(140),
`subcontracting_receipt_item` varchar(140),
`rejected_warehouse` varchar(140),
`bom` varchar(140),
`include_exploded_items` int(1) not null default 0,
`quality_inspection` varchar(140),
`schedule_date` date,
`reference_name` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`rejected_serial_and_batch_bundle` varchar(140),
`serial_no` text,
`rejected_serial_no` text,
`batch_no` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`expense_account` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` int(1) not null default 0,
`purchase_order` varchar(140),
`purchase_order_item` varchar(140),
index `item_code`(`item_code`),
index `subcontracting_order`(`subcontracting_order`),
index `subcontracting_order_item`(`subcontracting_order_item`),
index `purchase_order`(`purchase_order`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:29,118 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_delivery_note` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`is_return` int(1) not null default 0,
`return_against` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`set_warehouse` varchar(140),
`rejected_warehouse` varchar(140),
`supplier_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`bill_no` varchar(140),
`bill_date` date,
`supplier_address` varchar(140),
`contact_person` varchar(140),
`address_display` text,
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`distribute_additional_costs_based_on` varchar(140) default 'Qty',
`total_additional_costs` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`range` varchar(140),
`represents_company` varchar(140),
`status` varchar(140) default 'Draft',
`per_returned` decimal(21,9) not null default 0,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`language` varchar(140),
`instructions` text,
`select_print_heading` varchar(140),
`remarks` text,
`transporter_name` varchar(140),
`lr_no` varchar(140),
`lr_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `posting_date`(`posting_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:29,234 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Supplied Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`reserve_warehouse` varchar(140),
`bom_detail_no` varchar(140),
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`supplied_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`total_supplied_qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:29,331 WARNING database DDL Query made to DB:
create table `tabSubcontracting BOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_active` int(1) not null default 1,
`finished_good` varchar(140),
`finished_good_qty` decimal(21,9) not null default 1.0,
`finished_good_uom` varchar(140),
`finished_good_bom` varchar(140),
`service_item` varchar(140),
`service_item_qty` decimal(21,9) not null default 1.0,
`service_item_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `finished_good`(`finished_good`),
index `finished_good_bom`(`finished_good_bom`),
index `service_item`(`service_item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:29,469 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`purchase_order` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_warehouse` varchar(140),
`company` varchar(140),
`transaction_date` date,
`schedule_date` date,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`set_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`set_reserve_warehouse` varchar(140),
`supplier_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`distribute_additional_costs_based_on` varchar(140) default 'Qty',
`total_additional_costs` decimal(21,9) not null default 0,
`status` varchar(140) default 'Draft',
`per_received` decimal(21,9) not null default 0,
`select_print_heading` varchar(140),
`letter_head` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:29,568 WARNING database DDL Query made to DB:
create table `tabCommon Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_list` varchar(140),
`title` varchar(300),
`common_code` varchar(300),
`description` text,
`additional_data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_list`(`code_list`),
index `common_code`(`common_code`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:29,625 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommon Code`
				ADD INDEX IF NOT EXISTS `code_list_common_code_index`(code_list, common_code)
2025-06-02 11:04:29,686 WARNING database DDL Query made to DB:
create table `tabCode List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`canonical_uri` varchar(140),
`url` varchar(140),
`default_common_code` varchar(140),
`version` varchar(140),
`publisher` varchar(140),
`publisher_id` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:34,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` ADD COLUMN `tax_category` varchar(140), ADD COLUMN `is_your_company_address` int(1) not null default 0
2025-06-02 11:04:34,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` ADD COLUMN `is_billing_contact` int(1) not null default 0
2025-06-02 11:04:34,950 WARNING database DDL Query made to DB:
create table `tabJob Requisition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`designation` varchar(140),
`department` varchar(140),
`no_of_positions` int(11) not null default 0,
`expected_compensation` decimal(21,9) not null default 0,
`company` varchar(140),
`status` varchar(140),
`requested_by` varchar(140),
`requested_by_name` varchar(140),
`requested_by_dept` varchar(140),
`requested_by_designation` varchar(140),
`posting_date` date,
`completed_on` date,
`expected_by` date,
`time_to_fill` decimal(21,9),
`description` longtext,
`reason_for_requesting` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,049 WARNING database DDL Query made to DB:
create table `tabEmployee Boarding Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity_name` varchar(140),
`user` varchar(140),
`role` varchar(140),
`begin_on` int(11) not null default 0,
`duration` int(11) not null default 0,
`task` varchar(140),
`task_weight` decimal(21,9) not null default 0,
`required_for_employee_creation` int(1) not null default 0,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,124 WARNING database DDL Query made to DB:
create table `tabJob Offer Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`offer_term` varchar(140),
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,225 WARNING database DDL Query made to DB:
create table `tabAppraisal Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cycle_name` varchar(140) unique,
`company` varchar(140),
`status` varchar(140) default 'Not Started',
`start_date` date,
`end_date` date,
`description` longtext,
`kra_evaluation_method` varchar(140) default 'Automated Based on Goal Progress',
`calculate_final_score_based_on_formula` int(1) not null default 0,
`final_score_formula` longtext,
`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,364 WARNING database DDL Query made to DB:
create table `tabAppointment Letter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_applicant` varchar(140),
`applicant_name` varchar(140),
`company` varchar(140),
`appointment_date` date,
`appointment_letter_template` varchar(140),
`introduction` longtext,
`closing_notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,440 WARNING database DDL Query made to DB:
create table `tabExpected Skill Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140),
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,510 WARNING database DDL Query made to DB:
create table `tabAppointment Letter content` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,586 WARNING database DDL Query made to DB:
create table `tabLeave Block List Date` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`block_date` date,
`reason` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,667 WARNING database DDL Query made to DB:
create table `tabJob Offer Term Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,764 WARNING database DDL Query made to DB:
create table `tabLeave Block List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_block_list_name` varchar(140) unique,
`company` varchar(140),
`applies_to_all_departments` int(1) not null default 0,
`leave_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:35,845 WARNING database DDL Query made to DB:
create table `tabEmployee Separation Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
