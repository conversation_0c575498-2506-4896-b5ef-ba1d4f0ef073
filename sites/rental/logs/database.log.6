2025-06-02 11:33:12,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Detail` DROP INDEX `modified`
2025-06-02 11:33:12,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabCashier Closing Payments` DROP INDEX `modified`
2025-06-02 11:33:12,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Components` DROP INDEX `modified`
2025-06-02 11:33:12,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Overtime` DROP INDEX `modified`
2025-06-02 11:33:12,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Components` DROP INDEX `modified`
2025-06-02 11:33:12,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabHomepage Section Card` DROP INDEX `modified`
2025-06-02 11:33:12,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Criteria` DROP INDEX `modified`
2025-06-02 11:33:12,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Variable` DROP INDEX `modified`
2025-06-02 11:33:12,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Standing` DROP INDEX `modified`
2025-06-02 11:33:13,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` DROP INDEX `modified`
2025-06-02 11:33:13,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Detail` DROP INDEX `modified`
2025-06-02 11:33:13,066 WARNING database DDL Query made to DB:
ALTER TABLE `tabLost Reason Detail` DROP INDEX `modified`
2025-06-02 11:33:13,098 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Tax Template Detail` DROP INDEX `modified`
2025-06-02 11:33:13,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabPractitioner Service Unit Schedule` DROP INDEX `modified`
2025-06-02 11:33:13,166 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction Mapping` DROP INDEX `modified`
2025-06-02 11:33:13,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabOrder Tracking Container` DROP INDEX `modified`
2025-06-02 11:33:13,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Reading` DROP INDEX `modified`
2025-06-02 11:33:13,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Details` DROP INDEX `modified`
2025-06-02 11:33:13,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` DROP INDEX `modified`
2025-06-02 11:33:13,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabLinked Location` DROP INDEX `modified`
2025-06-02 11:33:13,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` DROP INDEX `modified`
2025-06-02 11:33:13,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` DROP INDEX `modified`
2025-06-02 11:33:13,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupport Search Source` DROP INDEX `modified`
2025-06-02 11:33:13,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Team Member` DROP INDEX `modified`
2025-06-02 11:33:13,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category Account` DROP INDEX `modified`
2025-06-02 11:33:13,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract Fulfilment Checklist` DROP INDEX `modified`
2025-06-02 11:33:13,577 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract Template Fulfilment Terms` DROP INDEX `modified`
2025-06-02 11:33:13,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabClosed Document` DROP INDEX `modified`
2025-06-02 11:33:13,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Point Entry Redemption` DROP INDEX `modified`
2025-06-02 11:33:13,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Customer Detail` DROP INDEX `modified`
2025-06-02 11:33:13,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` DROP INDEX `modified`
2025-06-02 11:33:13,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabMode of Payment Account` DROP INDEX `modified`
2025-06-02 11:33:13,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip Location Update` DROP INDEX `modified`
2025-06-02 11:33:13,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabCash Flow Mapping Accounts` DROP INDEX `modified`
2025-06-02 11:33:13,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer File Closing Information` DROP INDEX `modified`
2025-06-02 11:33:13,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Status Table` DROP INDEX `modified`
2025-06-02 11:33:14,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Program Collection` DROP INDEX `modified`
2025-06-02 11:33:14,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabShare Balance` DROP INDEX `modified`
2025-06-02 11:33:14,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` DROP INDEX `modified`
2025-06-02 11:33:14,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Material Request` DROP INDEX `modified`
2025-06-02 11:33:14,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sales Order` DROP INDEX `modified`
2025-06-02 11:33:14,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabSensitivity Test Result` DROP INDEX `modified`
2025-06-02 11:33:14,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` DROP INDEX `modified`
2025-06-02 11:33:14,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` DROP INDEX `modified`
2025-06-02 11:33:14,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` DROP INDEX `modified`
2025-06-02 11:33:14,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabDosage Strength` DROP INDEX `modified`
2025-06-02 11:33:14,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` DROP INDEX `modified`
2025-06-02 11:33:14,378 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` DROP INDEX `modified`
2025-06-02 11:33:14,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` DROP INDEX `modified`
2025-06-02 11:33:14,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond History Table` DROP INDEX `modified`
2025-06-02 11:33:14,462 WARNING database DDL Query made to DB:
ALTER TABLE `tabMandatory Attachment Table` DROP INDEX `modified`
2025-06-02 11:33:14,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Documents` DROP INDEX `modified`
2025-06-02 11:33:14,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabPermits Table` DROP INDEX `modified`
2025-06-02 11:33:14,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport Border Procedure Table` DROP INDEX `modified`
2025-06-02 11:33:14,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Procedure Table` DROP INDEX `modified`
2025-06-02 11:33:14,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubtrips Table` DROP INDEX `modified`
2025-06-02 11:33:14,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabEquipment Table` DROP INDEX `modified`
2025-06-02 11:33:14,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequired Permit` DROP INDEX `modified`
2025-06-02 11:33:14,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Website Operation` DROP INDEX `modified`
2025-06-02 11:33:14,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Website Item` DROP INDEX `modified`
2025-06-02 11:33:14,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Item Group` DROP INDEX `modified`
2025-06-02 11:33:14,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Customer Group` DROP INDEX `modified`
2025-06-02 11:33:14,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Item Group` DROP INDEX `modified`
2025-06-02 11:33:14,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabUOM Conversion Detail` DROP INDEX `modified`
2025-06-02 11:33:14,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule Country` DROP INDEX `modified`
2025-06-02 11:33:14,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule Condition` DROP INDEX `modified`
2025-06-02 11:33:15,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice List Country` DROP INDEX `modified`
2025-06-02 11:33:15,066 WARNING database DDL Query made to DB:
ALTER TABLE `tabMonthly Distribution Percentage` DROP INDEX `modified`
2025-06-02 11:33:15,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Website Specification` DROP INDEX `modified`
2025-06-02 11:33:15,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant` DROP INDEX `modified`
2025-06-02 11:33:15,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Attribute Value` DROP INDEX `modified`
2025-06-02 11:33:15,196 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Internal Work History` DROP INDEX `modified`
2025-06-02 11:33:15,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee External Work History` DROP INDEX `modified`
2025-06-02 11:33:15,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Education` DROP INDEX `modified`
2025-06-02 11:33:16,612 WARNING database DDL Query made to DB:
ALTER TABLE __UserSettings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
2025-06-02 11:33:16,805 WARNING database DDL Query made to DB:
truncate `tabAccount Closing Balance`
2025-06-02 11:33:18,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` ADD COLUMN `healthcare_practitioner` varchar(140)
2025-06-02 11:33:18,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-06-02 11:33:18,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` ADD COLUMN `healthcare_service_unit` varchar(140)
2025-06-02 11:33:18,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-06-02 11:33:19,034 WARNING database DDL Query made to DB:
truncate `tabAdvance Payment Ledger Entry`
2025-06-02 11:33:19,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_datetime_creation_index`
2025-06-02 11:33:19,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `posting_datetime_creation_index`(posting_datetime, creation)
2025-06-02 11:33:20,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-06-02 11:33:20,864 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `healthcare_practitioner` varchar(140)
2025-06-02 11:33:20,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `actual_encashable_days` decimal(21,9) not null default 0
2025-06-02 11:33:20,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `healthcare_service_unit` varchar(140)
2025-06-02 11:33:21,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-06-02 11:33:21,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `other_allowance` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `worker_subsistence` decimal(21,9) not null default 0
2025-06-02 11:33:21,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0
2025-06-02 11:33:21,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-02 11:33:23,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_control_account` varchar(140)
2025-06-02 11:33:23,056 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-06-02 11:33:23,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_is_trade_in` int(1) not null default 0
2025-06-02 11:33:23,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0
2025-06-02 11:33:23,338 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `custom_uom` varchar(140), ADD COLUMN `custom_trade_in_item` varchar(140), ADD COLUMN `custom_trade_in_qty` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_uom` varchar(140), ADD COLUMN `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `custom_total_trade_in_value` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_batch_no` varchar(140), ADD COLUMN `custom_trade_in_serial_no` text
2025-06-02 11:33:23,362 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
2025-06-02 11:33:23,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `custom_sales_invoice` varchar(140)
2025-06-02 11:33:23,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0
2025-06-02 11:33:26,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:33:28,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `allow_negative` int(1) not null default 0
2025-06-02 11:33:28,857 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:33:29,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_bank_charges_account` varchar(140)
2025-06-02 11:33:29,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0
2025-06-02 11:33:29,362 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `bank_charges` decimal(21,9) not null default 0, ADD COLUMN `bank_charges_journal_entry` varchar(140), ADD COLUMN `bank_charges_description` text
2025-06-02 11:33:29,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-02 11:33:29,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD COLUMN `travel_request_ref` varchar(140)
2025-06-02 11:33:29,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-06-02 11:33:29,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD COLUMN `employee_advance_ref` varchar(140), ADD COLUMN `total_travel_cost` decimal(21,9) not null default 0
2025-06-02 11:33:29,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `max_unclaimed_ea` int(11) not null default 0
2025-06-02 11:33:29,884 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-06-02 11:33:30,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `worker_subsistence` decimal(21,9) not null default 0, MODIFY `other_allowance` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0
2025-06-02 11:33:30,317 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `enable_auto_close_material_request` int(1) not null default 0, ADD COLUMN `close_material_request_after` int(11) not null default 0
2025-06-02 11:33:30,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0
2025-06-02 11:33:30,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0
2025-06-02 11:33:31,079 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `admission_type` varchar(140)
2025-06-02 11:33:31,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 11:33:31,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Procedure Prescription` ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0
2025-06-02 11:33:31,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Procedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:33:31,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text
2025-06-02 11:33:31,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9), MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-06-02 11:33:32,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` ADD COLUMN `ward_type` varchar(140)
2025-06-02 11:33:32,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:33:32,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `shmh_organogram_department` varchar(140)
2025-06-02 11:33:32,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0
2025-06-02 11:33:32,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` ADD COLUMN `approval_date` date
2025-06-02 11:33:33,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` MODIFY `consumable_total_amount` decimal(21,9) not null default 0
2025-06-02 11:33:33,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `approval_date` date, ADD COLUMN `vehicle` varchar(140), ADD COLUMN `shmh_organogram_department` varchar(140)
2025-06-02 11:33:33,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `last_qty_prescribed` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `original_stock_uom_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `recommended_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-06-02 11:33:33,839 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD COLUMN `insurance_provider` varchar(140), ADD COLUMN `national_id` varchar(140) unique
2025-06-02 11:33:33,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD UNIQUE INDEX IF NOT EXISTS national_id (`national_id`)
2025-06-02 11:33:34,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0
2025-06-02 11:33:34,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 11:33:34,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `national_id` varchar(140), ADD COLUMN `jubilee_patient_claim` varchar(140), ADD COLUMN `years_of_insurance` int(11) not null default 0, ADD COLUMN `require_fingerprint` int(1) not null default 0, ADD COLUMN `require_facial_recognation` int(1) not null default 0, ADD COLUMN `biometric_method` varchar(140), ADD COLUMN `fpcode` varchar(140)
2025-06-02 11:33:35,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 11:33:35,367 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0
2025-06-02 11:33:35,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 11:33:35,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `has_preapproval` int(1) not null default 0
2025-06-02 11:33:35,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `current_total` decimal(21,9) not null default 0, MODIFY `previous_total` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 11:33:36,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140)
2025-06-02 11:33:36,155 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:33:36,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Examination` ADD COLUMN `approval_date` date
2025-06-02 11:33:36,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Code` ADD COLUMN `is_non_specific` int(1) not null default 0
2025-06-02 11:33:36,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan` ADD COLUMN `prescribe` int(1) not null default 0
2025-06-02 11:33:37,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD COLUMN `room_type` varchar(140), ADD COLUMN `is_consultancy_chargeable` int(1) not null default 0, ADD COLUMN `is_service_chargeable` int(1) not null default 0
2025-06-02 11:33:37,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type` ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `required_input` varchar(140), ADD COLUMN `visit_type_name_alias` varchar(140), ADD COLUMN `maximum_visit_per_month` varchar(140), ADD COLUMN `requires_remarks` int(1) not null default 0, ADD COLUMN `requires_referral_no` int(1) not null default 0, ADD COLUMN `description` text, ADD COLUMN `has_followup_charges` int(1) not null default 0, ADD COLUMN `has_fasttrack_charges` int(1) not null default 0
2025-06-02 11:33:37,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `has_copayment` int(1) not null default 0
2025-06-02 11:33:37,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:33:38,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` ADD COLUMN `national_id` varchar(140), ADD COLUMN `date_loggedin_to_nhif` date, ADD COLUMN `shm_doctor_organogram_department` varchar(140)
2025-06-02 11:33:38,173 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS practitioner_name (`practitioner_name`)
2025-06-02 11:33:38,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `service_request` varchar(140), ADD COLUMN `vehicle` varchar(140), ADD COLUMN `shmh_organogram_department` varchar(140)
2025-06-02 11:33:38,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-02 11:33:38,990 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test` ADD COLUMN `approval_date` date
2025-06-02 11:33:39,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Subscription` ADD COLUMN `national_id` varchar(140), ADD COLUMN `verifier_id` varchar(140), ADD COLUMN `card_type_id` varchar(140), ADD COLUMN `card_type_name` varchar(140)
2025-06-02 11:33:39,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Subscription` MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-12 16:32:26,030 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_4e5b33133c0fbbea'@'localhost'
2025-06-12 16:32:41,074 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_4e5b33133c0fbbea`
2025-06-12 16:32:41,079 WARNING database DDL Query made to DB:
CREATE USER '_4e5b33133c0fbbea'@'localhost' IDENTIFIED BY 'CfIdPkiZZJ4qSUSA'
2025-06-12 16:32:41,083 WARNING database DDL Query made to DB:
CREATE DATABASE `_4e5b33133c0fbbea` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-06-12 17:24:50,273 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-12 17:24:50,301 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-13 08:51:42,523 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-13 10:07:53,158 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-13 10:10:18,732 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-13 11:31:04,387 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-13 11:31:04,999 WARNING database DDL Query made to DB:
ALTER TABLE `tabMember` ADD COLUMN `custom_auto_subscription` int(1) not null default 0
2025-06-13 11:31:05,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabMember` ADD COLUMN `employer_name` varchar(140)
2025-06-13 11:31:05,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event` ADD COLUMN `cpd_hour` varchar(140)
2025-06-13 11:31:05,316 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD COLUMN `cpd_hour` varchar(140)
2025-06-13 11:31:05,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `membership_id` varchar(140), ADD COLUMN `member_name` varchar(140)
2025-06-13 11:31:05,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-06-13 11:31:05,981 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` ADD COLUMN `member_name` varchar(140), ADD COLUMN `member` varchar(140)
2025-06-13 11:31:06,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` MODIFY `hours` decimal(21,9) not null default 0
2025-06-13 11:31:06,053 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` ADD COLUMN `member_name` varchar(140), ADD COLUMN `member` varchar(140)
2025-06-13 11:31:06,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Feedback` ADD COLUMN `member_name` varchar(140), ADD COLUMN `member` varchar(140)
2025-06-13 11:31:07,786 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-13 11:31:08,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts` ADD COLUMN `categorize_by` varchar(140) default 'Categorize by Voucher (Consolidated)'
2025-06-13 11:31:08,636 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0
2025-06-13 11:31:09,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-13 11:31:09,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0
2025-06-13 11:31:10,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-06-13 11:31:10,655 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-13 11:31:11,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-13 11:31:11,061 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-13 11:31:11,714 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0, ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-13 11:31:11,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-06-13 11:31:12,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-13 11:31:12,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-13 11:31:12,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-13 11:31:12,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-06-13 11:31:13,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-13 11:31:13,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `professional` decimal(21,9) not null default 0, MODIFY `advance` decimal(21,9) not null default 0, MODIFY `african_life_assurance` decimal(21,9) not null default 0, MODIFY `meal_allowance` decimal(21,9) not null default 0, MODIFY `duty_special` decimal(21,9) not null default 0, MODIFY `other_allowance` decimal(21,9) not null default 0, MODIFY `risk_special` decimal(21,9) not null default 0, MODIFY `credit_society` decimal(21,9) not null default 0, MODIFY `worker_subsistence` decimal(21,9) not null default 0, MODIFY `responsibility` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `housing` decimal(21,9) not null default 0, MODIFY `radiology` decimal(21,9) not null default 0
2025-06-13 11:31:13,970 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `completed_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operation_time` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `batch_size` decimal(21,9) not null default 0
2025-06-13 11:31:14,162 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` ADD COLUMN `ordered_qty` decimal(21,9) not null default 0
2025-06-13 11:31:14,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `wo_produced_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0
2025-06-13 11:31:14,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0
2025-06-13 11:31:14,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `qty_after_transaction` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_value_difference` decimal(21,9) not null default 0, MODIFY `outgoing_rate` decimal(21,9) not null default 0
2025-06-13 11:31:14,970 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_code`, DROP INDEX `warehouse`, DROP INDEX `posting_date_index`
2025-06-13 11:31:31,844 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-06-13 11:31:38,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-13 11:31:38,363 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0
2025-06-13 11:31:44,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category` ADD COLUMN `non_depreciable_category` int(1) not null default 0
2025-06-13 11:31:45,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0
2025-06-13 11:31:49,704 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `expense_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-06-13 11:31:50,560 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `current_stock` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-13 11:31:50,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` MODIFY `time_to_fill` decimal(21,9), MODIFY `expected_compensation` decimal(21,9) not null default 0
2025-06-13 11:31:50,880 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:51,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` MODIFY `task_weight` decimal(21,9) not null default 0
2025-06-13 11:31:51,134 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:51,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:51,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:51,941 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:52,136 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:52,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:52,509 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:52,708 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term Template` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:52,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:53,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation Template` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:53,430 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Request` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:53,636 WARNING database DDL Query made to DB:
ALTER TABLE `tabPWA Notification` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:53,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` MODIFY `average_rating` decimal(3,2)
2025-06-13 11:31:53,853 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:54,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:54,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` MODIFY `longitude` decimal(21,9) not null default 0, MODIFY `latitude` decimal(21,9) not null default 0
2025-06-13 11:31:56,204 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:56,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-13 11:31:56,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:56,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `pay_via_payment_entry` int(1) not null default 0, ADD COLUMN `expense_account` varchar(140), ADD COLUMN `payable_account` varchar(140), ADD COLUMN `posting_date` date, ADD COLUMN `paid_amount` decimal(21,9) not null default 0, ADD COLUMN `cost_center` varchar(140), ADD COLUMN `status` varchar(140)
2025-06-13 11:31:56,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-06-13 11:31:56,875 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:57,161 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter Template` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:57,427 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0
2025-06-13 11:31:57,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:57,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:57,983 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:58,222 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` MODIFY `cost` decimal(21,9) not null default 0, MODIFY `actual_cost` decimal(21,9) not null default 0
2025-06-13 11:31:58,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:58,609 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding Template` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:58,847 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Referral` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:59,126 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Period` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:59,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service Item` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:59,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `sanctioned_amount` decimal(21,9) not null default 0
2025-06-13 11:31:59,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` ADD INDEX `creation`(`creation`)
2025-06-13 11:31:59,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Itinerary` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:00,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Feedback` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:00,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Training` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:00,329 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` MODIFY `goal_score` decimal(21,9) not null default 0, MODIFY `goal_completion` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-13 11:32:00,366 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:00,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` MODIFY `amount` decimal(21,9) not null default 0
2025-06-13 11:32:00,638 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:00,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` MODIFY `progress` decimal(21,9) not null default 0
2025-06-13 11:32:00,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:02,361 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` MODIFY `expected_average_rating` decimal(3,2), MODIFY `average_rating` decimal(3,2)
2025-06-13 11:32:02,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:04,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` MODIFY `annual_allocation` decimal(21,9) not null default 0
2025-06-13 11:32:05,041 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:06,544 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` MODIFY `rating` decimal(3,2)
2025-06-13 11:32:07,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:08,745 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD UNIQUE INDEX IF NOT EXISTS source_name (`source_name`)
2025-06-13 11:32:09,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:10,698 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD UNIQUE INDEX IF NOT EXISTS interest (`interest`)
2025-06-13 11:32:11,276 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:12,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabKRA` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:13,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Approver` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:15,114 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Criteria` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:16,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:17,544 WARNING database DDL Query made to DB:
ALTER TABLE `tabExit Interview` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:19,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:20,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` MODIFY `lower_range` decimal(21,9) not null default 0, MODIFY `upper_range` decimal(21,9) not null default 0
2025-06-13 11:32:21,668 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:21,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` MODIFY `total_leave_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-06-13 11:32:21,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:22,150 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` MODIFY `expected_average_rating` decimal(3,2)
2025-06-13 11:32:22,177 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:22,346 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD UNIQUE INDEX IF NOT EXISTS offer_term (`offer_term`)
2025-06-13 11:32:22,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:22,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_advance_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_claimed_amount` decimal(21,9) not null default 0, MODIFY `total_amount_reimbursed` decimal(21,9) not null default 0, MODIFY `total_sanctioned_amount` decimal(21,9) not null default 0
2025-06-13 11:32:22,712 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:22,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` MODIFY `unused_leaves` decimal(21,9) not null default 0, MODIFY `total_leaves_allocated` decimal(21,9) not null default 0, MODIFY `new_leaves_allocated` decimal(21,9) not null default 0, MODIFY `carry_forwarded_leaves_count` decimal(21,9) not null default 0, MODIFY `total_leaves_encashed` decimal(21,9) not null default 0
2025-06-13 11:32:23,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:23,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:23,416 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD UNIQUE INDEX IF NOT EXISTS identification_document_type (`identification_document_type`)
2025-06-13 11:32:23,453 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:23,667 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:23,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `score` decimal(21,9) not null default 0, MODIFY `score_earned` decimal(21,9) not null default 0
2025-06-13 11:32:23,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:24,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grievance` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:24,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Type` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:24,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-13 11:32:24,807 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:24,947 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` MODIFY `proficiency` decimal(3,2)
2025-06-13 11:32:24,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:25,175 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployment Type` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:25,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:25,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabGrievance Type` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:25,800 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` MODIFY `upper_range` decimal(21,9) not null default 0, MODIFY `applicant_rating` decimal(3,2), MODIFY `lower_range` decimal(21,9) not null default 0
2025-06-13 11:32:25,831 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:26,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:26,250 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD COLUMN `half_day_status` varchar(140), ADD COLUMN `modify_half_day_status` int(1) not null default 0
2025-06-13 11:32:26,275 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-13 11:32:27,483 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:27,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Property History` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:27,922 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group User` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:28,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` MODIFY `working_hours_threshold_for_absent` decimal(21,9) not null default 0, MODIFY `working_hours_threshold_for_half_day` decimal(21,9) not null default 0
2025-06-13 11:32:28,148 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:28,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:28,655 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` MODIFY `leaves` decimal(21,9) not null default 0
2025-06-13 11:32:28,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:28,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:29,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill Map` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:29,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` MODIFY `default_base_pay` decimal(21,9) not null default 0
2025-06-13 11:32:29,293 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:29,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Assignment` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:29,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:29,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` MODIFY `hours` decimal(21,9) not null default 0
2025-06-13 11:32:29,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:30,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterviewer` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:30,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompensatory Leave Request` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:30,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` MODIFY `estimated_cost_per_position` decimal(21,9) not null default 0, MODIFY `total_estimated_cost` decimal(21,9) not null default 0
2025-06-13 11:32:30,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:30,758 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Type` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:30,957 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD UNIQUE INDEX IF NOT EXISTS purpose_of_travel (`purpose_of_travel`)
2025-06-13 11:32:30,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:31,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Account` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:31,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` MODIFY `revised_ctc` decimal(21,9) not null default 0, MODIFY `current_ctc` decimal(21,9) not null default 0
2025-06-13 11:32:31,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:31,625 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` MODIFY `maximum_carry_forwarded_leaves` decimal(21,9) not null default 0, MODIFY `fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0, MODIFY `max_leaves_allowed` decimal(21,9) not null default 0
2025-06-13 11:32:31,661 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:31,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `unclaimed_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-06-13 11:32:31,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:32,033 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Transfer` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:32,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:32,509 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:32,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation Skill` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:32,944 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` MODIFY `total_asset_recovery_cost` decimal(21,9) not null default 0, MODIFY `total_receivable_amount` decimal(21,9) not null default 0, MODIFY `total_payable_amount` decimal(21,9) not null default 0
2025-06-13 11:32:32,978 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:35,092 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-13 11:32:35,136 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:37,209 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` MODIFY `total_score` decimal(21,9) not null default 0
2025-06-13 11:32:37,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:40,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` MODIFY `message` longtext default '<p>Please share what did you do today. If you reply by midnight, your response will be recorded!</p>'
2025-06-13 11:32:41,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:42,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD UNIQUE INDEX IF NOT EXISTS health_insurance_name (`health_insurance_name`)
2025-06-13 11:32:43,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:44,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Detail` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:45,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` MODIFY `rating` decimal(3,2), MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-13 11:32:45,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:46,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD UNIQUE INDEX IF NOT EXISTS training_program (`training_program`)
2025-06-13 11:32:47,641 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:48,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-13 11:32:49,754 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:50,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` MODIFY `max_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-13 11:32:50,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:50,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-13 11:32:50,875 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:51,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:51,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` MODIFY `amount` decimal(21,9) not null default 0
2025-06-13 11:32:51,337 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:51,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` MODIFY `exemption_amount` decimal(21,9) not null default 0, MODIFY `total_actual_amount` decimal(21,9) not null default 0
2025-06-13 11:32:51,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:51,799 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:52,003 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-13 11:32:52,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:52,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-13 11:32:52,428 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:52,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` MODIFY `base` decimal(21,9) not null default 0, MODIFY `taxable_earnings_till_date` decimal(21,9) not null default 0, MODIFY `variable` decimal(21,9) not null default 0, MODIFY `tax_deducted_till_date` decimal(21,9) not null default 0
2025-06-13 11:32:52,673 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:52,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` MODIFY `bonus_amount` decimal(21,9) not null default 0
2025-06-13 11:32:52,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:53,285 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0
2025-06-13 11:32:53,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:53,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` MODIFY `max_amount_eligible` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-06-13 11:32:53,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:54,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` MODIFY `incentive_amount` decimal(21,9) not null default 0
2025-06-13 11:32:54,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:54,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:54,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:54,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:55,079 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `auto_repeat_end_date` date, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0
2025-06-13 11:32:55,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:55,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-13 11:32:55,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:55,696 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` MODIFY `total_declared_amount` decimal(21,9) not null default 0, MODIFY `total_exemption_amount` decimal(21,9) not null default 0
2025-06-13 11:32:55,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:55,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` MODIFY `to_amount` decimal(21,9) not null default 0
2025-06-13 11:32:55,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:56,119 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-13 11:32:56,155 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:56,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` MODIFY `max_taxable_income` decimal(21,9) not null default 0, MODIFY `min_taxable_income` decimal(21,9) not null default 0, MODIFY `percent` decimal(21,9) not null default 0
2025-06-13 11:32:56,380 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:56,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `additional_amount` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0
2025-06-13 11:32:57,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:58,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:58,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:58,393 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` MODIFY `available_leaves` decimal(21,9) not null default 0, MODIFY `used_leaves` decimal(21,9) not null default 0, MODIFY `expired_leaves` decimal(21,9) not null default 0, MODIFY `pending_leaves` decimal(21,9) not null default 0, MODIFY `total_allocated_leaves` decimal(21,9) not null default 0
2025-06-13 11:32:58,425 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:58,633 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` MODIFY `pro_rata_dispensed_amount` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `remaining_benefit` decimal(21,9) not null default 0
2025-06-13 11:32:58,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:58,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-13 11:32:58,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:59,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` ADD COLUMN `tax_relief_limit` decimal(21,9) not null default 0
2025-06-13 11:32:59,235 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0
2025-06-13 11:32:59,271 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` ADD INDEX `creation`(`creation`)
2025-06-13 11:32:59,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` MODIFY `max_amount` decimal(21,9) not null default 0
