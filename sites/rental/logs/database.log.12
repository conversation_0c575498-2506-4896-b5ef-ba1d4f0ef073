2025-06-02 11:05:59,059 WARNING database DDL Query made to DB:
create table `tabFabrizio Test Form` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`frmdate` date,
`frmname` varchar(140),
`frmsurname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `frmname`(`frmname`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:59,171 WARNING database DDL Query made to DB:
create table `tabImport File` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`import_file_number` varchar(140) unique,
`bl_number` varchar(140),
`no_of_containers` varchar(140),
`tansad_no` varchar(140),
`clearing_agent_name` varchar(140),
`supplier_name` varchar(140),
`supplier_invoice_no` varchar(140),
`invoice_currency` varchar(140),
`invoice_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:59,272 WARNING database DDL Query made to DB:
create table `tabBorder Procedure Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:59,386 WARNING database DDL Query made to DB:
create table `tabTerminal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`terminal` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:59,659 WARNING database DDL Query made to DB:
create table `tabImport` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_file_number` varchar(140),
`company` varchar(140),
`point_of_entry` varchar(140),
`bl_number` varchar(140),
`original_bl_received` date,
`location` varchar(140),
`documents_received_date` date,
`border_customer` varchar(140),
`reference_trip` varchar(140),
`cargo_type` varchar(140),
`shipper` varchar(140),
`consignee` varchar(140),
`notify_party` varchar(140),
`customer` varchar(140),
`clearing_agent_border_1` varchar(140),
`clearing_agent_border_2` varchar(140),
`clearing_agent_border_3` varchar(140),
`house_bill_of_lading` varchar(140),
`shipping_line` varchar(140),
`vessel_name` varchar(140),
`voyage` varchar(140),
`cargo_destination_country` varchar(140),
`cargo_destination_city` varchar(140),
`port_of_loading` varchar(140),
`port_of_discharge` varchar(140),
`eta` date,
`ata` date,
`discharge` date,
`terminal` varchar(140),
`icd` varchar(140),
`carry_in_date` date,
`demurrage_start_date` date,
`cargo` varchar(140),
`cargo_description` varchar(140),
`container_owner` varchar(140),
`amended_from` varchar(140),
`import_type` varchar(140),
`declaration_type` varchar(140),
`assessment_submission_date` date,
`manifest_date` date,
`tansad_number` varchar(140),
`tansad_date` date,
`manifest_comparison_date` date,
`final_assessment_obtained` date,
`lodged_for_verification` date,
`verification_date` date,
`customs_release_date` date,
`lodged_for_port_charges` date,
`port_charges_payment` date,
`transport_requested` varchar(140),
`t1_generated` date,
`t1_approved` date,
`bt_number` varchar(140),
`lodged_for_do_invoice` date,
`invoice_received` date,
`lodged_for_do` date,
`do_obtained` date,
`do_reference` varchar(140),
`storing_order_obtained` date,
`storing_order_validity` date,
`employee` varchar(140),
`employee_name` varchar(140),
`lodged_for_port_invoice` date,
`invoice_obtained` date,
`lodged_for_loading_permit` date,
`loading_permit_obtained` date,
`loading_date` date,
`loading_time` time(6),
`loading_permit_handover` date,
`loading_permit_handed_to_transporter_time` time(6),
`permit_received_by` varchar(140),
`special_instructions_to_transporter` text,
`status` varchar(140) default 'Open',
`cheque_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:59,766 WARNING database DDL Query made to DB:
create table `tabImport Border Procedure Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
`date` date,
`attachment` text,
`extra_comment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:59,864 WARNING database DDL Query made to DB:
create table `tabReporting Status Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`datetime` datetime(6),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:59,969 WARNING database DDL Query made to DB:
create table `tabCargo Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_number` varchar(140),
`container_size` varchar(140),
`seal_number` varchar(140),
`cargo_status` varchar(140),
`no_of_packages` int(11) not null default 0,
`gross_weight` decimal(21,9) not null default 0,
`net_weight` decimal(21,9) not null default 0,
`tare_weight` decimal(21,9) not null default 0,
`assigned_vehicle` varchar(140),
`assigned_driver` varchar(140),
`transport_status` int(11) not null default 0,
`bond_ref_no` varchar(140),
`bond_value` decimal(21,9) not null default 0,
`extra_details` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:00,058 WARNING database DDL Query made to DB:
create table `tabContainer Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:00,160 WARNING database DDL Query made to DB:
create table `tabContainer Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{purpose}',
`naming_series` varchar(140),
`purpose` varchar(140),
`collection_date` date,
`shipping_line` varchar(140),
`export_reference` varchar(140),
`booking_number` varchar(140),
`amended_from` varchar(140),
`container_reference` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:00,264 WARNING database DDL Query made to DB:
create table `tabBorder Processing Vehicle Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`transporter` varchar(140),
`driver` varchar(140),
`driver_contact` varchar(140),
`vehicle_no` varchar(140),
`plate_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:00,400 WARNING database DDL Query made to DB:
create table `tabBorder Processing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_file` varchar(140),
`creation_document_no` varchar(140),
`creation_date` date,
`cross_border_no` varchar(140),
`customer` varchar(140),
`arrival_date` date,
`departure_date` date,
`crossing_date` date,
`bond_cancellation_date` date,
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:00,527 WARNING database DDL Query made to DB:
create table `tabContainer File Closing Information` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_no` varchar(140),
`proof_of_delivery` text,
`container_return_date` date,
`inward_interchange_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:00,615 WARNING database DDL Query made to DB:
create table `tabICD` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`icd_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:00,856 WARNING database DDL Query made to DB:
create table `tabBorder Clearance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`billing_type` varchar(140),
`location` varchar(140),
`documents_received` datetime(6),
`crn_reference_number` varchar(140),
`no_of_borders` varchar(140),
`documents_submitted_by_driver` date,
`documents_submitted_by_driver_time` time(6),
`clearance_type` varchar(140),
`cargo_type` varchar(140),
`cargo_category` varchar(140),
`cargo_description` varchar(140),
`loose_cargo_category` varchar(140),
`goods_description` varchar(140),
`goods_quantity` decimal(21,9) not null default 0,
`goods_unit` varchar(140),
`loose_sub_t1_ref` varchar(140),
`loose_bond_value` varchar(140),
`loose_gross_weight` decimal(21,9) not null default 0,
`loose_net_weight` decimal(21,9) not null default 0,
`client` varchar(140),
`consignee` varchar(140),
`transporter_type` varchar(140),
`transporter_name` varchar(140),
`vehicle_plate_number` varchar(140),
`trailer_plate_number` varchar(140),
`driver_name` varchar(140),
`cargo_origin_country` varchar(140),
`cargo_origin_city` varchar(140),
`cargo_origin_exact` varchar(140),
`cargo_destination_country` varchar(140),
`cargo_destination_city` varchar(140),
`cargo_destination_exact` varchar(140),
`border1_name` varchar(140),
`border1_clearing_agent` varchar(140),
`border1_documents_submitted` datetime(6),
`border1_documents_received_by` varchar(140),
`border1_arrival` datetime(6),
`border1_departure` datetime(6),
`border1_crossing` datetime(6),
`border2_name` varchar(140),
`border2_clearing_agent` varchar(140),
`border2_documents_submitted` datetime(6),
`border2_documents_received_by` varchar(140),
`border2_arrival` datetime(6),
`border2_departure` datetime(6),
`border2_crossing` datetime(6),
`border3_name` varchar(140),
`border3_clearing_agent` varchar(140),
`border3_documents_submitted` datetime(6),
`border3_documents_received_by` varchar(140),
`border3_arrival` datetime(6),
`border3_departure` datetime(6),
`border3_crossing` datetime(6),
`border4_name` varchar(140),
`border4_clearing_agent` varchar(140),
`border4_documents_submitted` datetime(6),
`border4_documents_received_by` varchar(140),
`border4_arrival` datetime(6),
`border4_departure` datetime(6),
`border4_crossing` datetime(6),
`manifest_creation_date` date,
`manifest_approval_date` date,
`manifest_approval_time` time(6),
`customs_assessment_submited` date,
`final_assessment_received` date,
`tansad_number` varchar(140),
`verification_sealing_date` date,
`manifest_comparison_date` date,
`customs_release_date` date,
`lodge_for_t1` date,
`bt_approval_date` date,
`bond_ref_no` varchar(140),
`bond_value` varchar(140),
`bond_canceled_date` date,
`no_of_packages` varchar(140),
`offloading_point_arrival` datetime(6),
`offloading_datetime` datetime(6),
`closing_remarks` longtext,
`reference_trip_route` varchar(140),
`local_border` varchar(140),
`file_number` varchar(140),
`trip_reference_no` varchar(140),
`main_return_select` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:00,980 WARNING database DDL Query made to DB:
create table `tabBond` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_no` varchar(140) unique,
`bond_value` decimal(21,9) not null default 0,
`no_of_packages` int(11) not null default 0,
`cargo` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:01,079 WARNING database DDL Query made to DB:
create table `tabPacking List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_number` varchar(140),
`seal_number` varchar(140),
`no_of_bundles` varchar(140),
`net_weight` decimal(21,9) not null default 0,
`gross_weight` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:01,181 WARNING database DDL Query made to DB:
create table `tabCargo Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cargo_name` varchar(140),
`local_export` int(1) not null default 0,
`transit_export` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:01,268 WARNING database DDL Query made to DB:
create table `tabPort` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`port` varchar(140),
`country` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:01,357 WARNING database DDL Query made to DB:
create table `tabContainer Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_booking_no` varchar(140),
`to_booking_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:01,452 WARNING database DDL Query made to DB:
create table `tabShipping Line` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shipping_line_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:01,573 WARNING database DDL Query made to DB:
create table `tabMandatory Attachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:01,685 WARNING database DDL Query made to DB:
create table `tabPermits Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lodged` date,
`received` date,
`certificate` text,
`description` varchar(140),
`additional_notes` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:01,873 WARNING database DDL Query made to DB:
create table `tabFiles` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`documents_received_date` date,
`location` varchar(140),
`customer` varchar(140),
`quotation` varchar(140),
`requested_service` varchar(140),
`import_transit` int(1) not null default 0,
`import_local` int(1) not null default 0,
`export_transit` int(1) not null default 0,
`export_local` int(1) not null default 0,
`transport_transit` int(1) not null default 0,
`transport_local` int(1) not null default 0,
`border_clearance` int(1) not null default 0,
`cross_border_no` varchar(140),
`border_processing_reference` varchar(140),
`bl_number` varchar(140),
`import_reference` varchar(140),
`booking_number` varchar(140),
`export_reference` varchar(140),
`source_country` varchar(140),
`source_city` varchar(140),
`destination_country` varchar(140),
`destination_city` varchar(140),
`transport_reference` varchar(140),
`number_of_borders` int(11) not null default 0,
`border_clearance_ref` varchar(140),
`per_unit` varchar(140),
`status` varchar(140) default 'Open',
`sales_order_reference` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:01,996 WARNING database DDL Query made to DB:
create table `tabMandatory Attachment Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attachment_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:02,112 WARNING database DDL Query made to DB:
create table `tabBond History Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`no_of_bundles` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:02,415 WARNING database DDL Query made to DB:
create table `tabTrailer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number_plate` varchar(140) unique,
`chassis_number` varchar(140),
`make` varchar(140),
`year` int(11) not null default 0,
`axles` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:02,533 WARNING database DDL Query made to DB:
create table `tabTire Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tire_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:02,679 WARNING database DDL Query made to DB:
create table `tabTransport Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_received` date,
`customer` varchar(140),
`loading_date` date,
`transport_type` varchar(140),
`cargo_location_country` varchar(140),
`cargo_location_city` varchar(140),
`cargo_destination_country` varchar(140),
`cargo_destination_city` varchar(140),
`consignee` varchar(140),
`shipper` varchar(140),
`border1_clearing_agent` varchar(140),
`border2_clearing_agent` varchar(140),
`border3_clearing_agent` varchar(140),
`special_instructions_to_transporter` text,
`cargo_type` varchar(140),
`goods_description` varchar(140),
`cargo_description` varchar(140),
`amount` decimal(21,9) not null default 0,
`unit` varchar(140),
`total_assigned` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`cargo_name` varchar(140),
`file_number` varchar(140),
`assignment_status` varchar(140) default 'Waiting Assignment',
`version` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:02,776 WARNING database DDL Query made to DB:
create table `tabEngine Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:02,877 WARNING database DDL Query made to DB:
create table `tabEquipment Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`equipment` varchar(140),
`quantity` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:03,000 WARNING database DDL Query made to DB:
create table `tabVehicle Trip Location Update` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`timestamp` datetime(6),
`location` varchar(140),
`longitude` varchar(140),
`latitude` varchar(140),
`comment` varchar(140),
`type_of_update` varchar(140) default 'Manual Update',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:03,118 WARNING database DDL Query made to DB:
create table `tabPower Train Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`power_train_checklist` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:03,275 WARNING database DDL Query made to DB:
create table `tabVehicle Documents Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:03,396 WARNING database DDL Query made to DB:
create table `tabAir System Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:03,611 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD COLUMN `return_date` date, ADD COLUMN `return_reason` varchar(140), ADD COLUMN `odometer_on_return` int(11) not null default 0, ADD COLUMN `return_condition` text
2025-06-02 11:06:03,633 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 11:06:03,738 WARNING database DDL Query made to DB:
create table `tabBrake System Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:03,843 WARNING database DDL Query made to DB:
create table `tabTrip Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`location` varchar(140),
`is_local_border` int(1) not null default 0,
`is_international_border` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:03,968 WARNING database DDL Query made to DB:
create table `tabFuel Request Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`quantity` decimal(21,9) not null default 0,
`disburcement_type` varchar(140),
`supplier` varchar(140),
`cost_per_litre` decimal(21,9) not null default 0,
`total_cost` decimal(21,9) not null default 0,
`status` varchar(140) default 'Open',
`uom` varchar(140),
`approved_by` varchar(140),
`approved_date` date,
`receipt_date` date,
`receipt_time` time(6),
`received_by` varchar(140),
`transaction_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:04,059 WARNING database DDL Query made to DB:
create table `tabFuel System Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fuel_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:04,154 WARNING database DDL Query made to DB:
create table `tabSuspension Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parts` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:04,251 WARNING database DDL Query made to DB:
create table `tabElectronics Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electronics_part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:04,377 WARNING database DDL Query made to DB:
create table `tabVehicle Inspection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`driver_name` varchar(140),
`vehicle_plate_number` varchar(140),
`trailer_no` varchar(140),
`date` date,
`mileage` varchar(140),
`vehicle_type` varchar(140),
`image_upload` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:04,481 WARNING database DDL Query made to DB:
create table `tabTrip Steps Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location` varchar(140),
`distance` decimal(21,9) not null default 0,
`location_type` varchar(140),
`is_local_border` int(1) not null default 0,
`is_international_border` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:04,574 WARNING database DDL Query made to DB:
create table `tabBrake Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`brake_system` varchar(140),
`mark` int(1) not null default 0,
`brake_remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:04,693 WARNING database DDL Query made to DB:
create table `tabFuel Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_plate_number` varchar(140),
`company` varchar(140),
`main_route` varchar(140),
`main_approved_fuel` varchar(140),
`return_route` varchar(140),
`return_approved_fuel` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`status` varchar(140) default 'Waiting Approval',
`transaction_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:04,807 WARNING database DDL Query made to DB:
create table `tabEngine Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`engine_system` varchar(140),
`mark` int(1) not null default 0,
`engine_remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:04,916 WARNING database DDL Query made to DB:
create table `tabFuel System Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fuel_system` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:05,013 WARNING database DDL Query made to DB:
create table `tabLighting Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lighting_check_item` varchar(140),
`lighting_mark` int(1) not null default 0,
`lighting_remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:05,134 WARNING database DDL Query made to DB:
create table `tabAssigned Transport Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cargo` varchar(140),
`container_number` varchar(140),
`assigned_vehicle` varchar(140),
`assigned_trailer` varchar(140),
`assigned_driver` varchar(140),
`driver_name` varchar(140),
`passport_number` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`loading_date` date,
`lodge_permit` date,
`dispatch_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:05,231 WARNING database DDL Query made to DB:
create table `tabLighting Checklist Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:05,336 WARNING database DDL Query made to DB:
create table `tabAir System Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parts` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:05,447 WARNING database DDL Query made to DB:
create table `tabFixed Expense` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`currency` varchar(140),
`fixed_value` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:05,563 WARNING database DDL Query made to DB:
create table `tabVehicle Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:05,672 WARNING database DDL Query made to DB:
create table `tabTrip Location Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_type` varchar(140),
`arrival_date` int(1) not null default 0,
`departure_date` int(1) not null default 0,
`loading_date` int(1) not null default 0,
`offloading_date` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:05,784 WARNING database DDL Query made to DB:
create table `tabTire Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tire_position` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:05,908 WARNING database DDL Query made to DB:
create table `tabVehicle Inspection Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:06,022 WARNING database DDL Query made to DB:
create table `tabSuspension Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:06,114 WARNING database DDL Query made to DB:
create table `tabSteering Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`steering_part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:06,204 WARNING database DDL Query made to DB:
create table `tabEquipment Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`set_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:06,326 WARNING database DDL Query made to DB:
create table `tabExpense` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`fixed_value` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:06,433 WARNING database DDL Query made to DB:
create table `tabPower Train Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`power_train_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:06,535 WARNING database DDL Query made to DB:
create table `tabSteering Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`steering_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:06,628 WARNING database DDL Query made to DB:
create table `tabVehicle Documents` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`reference_no` varchar(140),
`issue_date` date,
`expire_date` date,
`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:06,838 WARNING database DDL Query made to DB:
create table `tabTransport Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cargo` varchar(140),
`amount` decimal(21,9) not null default 0,
`expected_loading_date` date,
`container_number` varchar(140),
`units` varchar(140),
`transporter_type` varchar(140),
`sub_contractor` varchar(140),
`assigned_vehicle` varchar(140),
`vehicle_plate_number` varchar(140),
`assigned_trailer` varchar(140),
`trailer_plate_number` varchar(140),
`assigned_driver` varchar(140),
`driver_name` varchar(140),
`passport_number` varchar(140),
`driver_licence` varchar(140),
`driver_contact` varchar(140),
`route` varchar(140),
`vehicle_status` int(11) not null default 0,
`vehicle_trip` varchar(140),
`status` varchar(140) default 'Not Processed',
`created_trip` varchar(140),
`loading_date` date,
`lodge_permit` date,
`dispatch_date` date,
`cargo_type` varchar(140),
`attach_1` text,
`attach_2` text,
`attach_3` text,
`attach_4` text,
`description_1` varchar(140),
`description_2` varchar(140),
`description_3` varchar(140),
`description_4` varchar(140),
`file_number` varchar(140),
`import` varchar(140),
`export` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:06,939 WARNING database DDL Query made to DB:
create table `tabTires Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:07,056 WARNING database DDL Query made to DB:
create table `tabElectrical Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electrical_part` varchar(140),
`mark` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:07,159 WARNING database DDL Query made to DB:
create table `tabVehicle Routine Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`data_1` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:07,265 WARNING database DDL Query made to DB:
create table `tabUnit of Measure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`unit_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:07,367 WARNING database DDL Query made to DB:
create table `tabFixed Expense Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense` varchar(140),
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:07,480 WARNING database DDL Query made to DB:
create table `tabRoute Steps Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location` varchar(140),
`location_type` varchar(140),
`arrival_date` date,
`departure_date` date,
`loading_date` date,
`offloading_date` date,
`documents_from_driver` datetime(6),
`doc_submitted_to_agent` date,
`doc_received_by` varchar(140),
`crossing_time` datetime(6),
`comment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:07,696 WARNING database DDL Query made to DB:
create table `tabVehicle Trip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`transporter_type` varchar(140),
`sub_contractor` varchar(140),
`vehicle` varchar(140),
`vehicle_plate_number` varchar(140),
`trailer` varchar(140),
`trailer_plate_number` varchar(140),
`driver` varchar(140),
`driver_name` varchar(140),
`passport_number` varchar(140),
`driving_licence_no` varchar(140),
`phone_number` varchar(140),
`start_date` date,
`main_customer` varchar(140),
`main_cargo_type` varchar(140),
`main_cargo_category` varchar(140),
`main_goods_description` varchar(140),
`main_amount` decimal(21,9) not null default 0,
`main_unit` varchar(140),
`main_loose_no_of_packages` int(11) not null default 0,
`main_loose_gross_weight` int(11) not null default 0,
`main_loose_net_weight` int(11) not null default 0,
`main_shipper` varchar(140),
`main_consignee` varchar(140),
`main_cargo_location_country` varchar(140),
`main_cargo_location_city` varchar(140),
`main_loading_point` varchar(140),
`main_cargo_destination_country` varchar(140),
`main_cargo_destination_city` varchar(140),
`main_offloading_point` varchar(140),
`main_eta` datetime(6),
`main_route` varchar(140),
`main_shipment_ref_no` varchar(140),
`main_client_ref_no` varchar(140),
`main_border1_clearing` varchar(140),
`main_border2_clearing` varchar(140),
`main_border3_clearing` varchar(140),
`main_special_instructions_transporter` varchar(140),
`main_special_instructions_to_driver` varchar(140),
`main_delivery_note` text,
`main_offloading_report` text,
`main_offloading_weight` date,
`main_approved_fuel` varchar(140),
`return_start_date` date,
`return_customer` varchar(140),
`return_cargo_type` varchar(140),
`return_cargo_category` varchar(140),
`return_goods_description` varchar(140),
`return_amount` decimal(21,9) not null default 0,
`return_unit` varchar(140),
`return_loose_no_of_packages` int(11) not null default 0,
`return_loose_gross_weight` int(11) not null default 0,
`return_loose_net_weight` int(11) not null default 0,
`return_shipper` varchar(140),
`return_consignee` varchar(140),
`return_cargo_location_country` varchar(140),
`return_cargo_location_city` varchar(140),
`return_loading_point` varchar(140),
`return_cargo_destination_country` varchar(140),
`return_cargo_destination_city` varchar(140),
`return_offloading_point` varchar(140),
`return_eta` date,
`return_route` varchar(140),
`return_shipment_ref_no` varchar(140),
`return_client_ref_no` varchar(140),
`return_border1_clearing` varchar(140),
`return_border2_clearing` varchar(140),
`return_border3_clearing` varchar(140),
`return_special_instructions_transporter` varchar(140),
`return_special_instructions_driver` varchar(140),
`return_delivery_note` text,
`return_offloading_report` text,
`return_offloading_weight` date,
`return_approved_fuel` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`main_file_number` varchar(140),
`main_import` varchar(140),
`main_export` varchar(140),
`return_reference_doctype` varchar(140),
`return_reference_docname` varchar(140),
`return_file_number` varchar(140),
`return_import` varchar(140),
`return_export` varchar(140),
`main_status` varchar(140),
`return_status` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Open',
`hidden_status` int(11) not null default 0,
`main_address_display` longtext,
`main_consignee_display` longtext,
`main_shipper_display` longtext,
`return_consignee_display` longtext,
`return_shipper_display` longtext,
`return_address_display` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:07,802 WARNING database DDL Query made to DB:
create table `tabTrip Route` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`route_name` varchar(140),
`total_distance` int(11) not null default 0,
`total_tzs` decimal(21,9) not null default 0,
`total_usd` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:07,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 11:06:08,075 WARNING database DDL Query made to DB:
create table `tabSubtrips Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_location` varchar(140),
`destination_location` varchar(140),
`approximate_distance` int(11) not null default 0,
`source_departure` date,
`destination_arrival` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:08,169 WARNING database DDL Query made to DB:
create table `tabElectrical Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electrical_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:08,257 WARNING database DDL Query made to DB:
create table `tabVehicle Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lighting_check_item` varchar(140),
`lighting_mark` int(1) not null default 0,
`brake_check_item` varchar(140),
`brake_mark` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:08,345 WARNING database DDL Query made to DB:
create table `tabTires Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:08,429 WARNING database DDL Query made to DB:
create table `tabElectronics Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electronic_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:08,696 WARNING database DDL Query made to DB:
create table `tabOrder Track` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier` varchar(140),
`supplier_type` varchar(140),
`expected_arrival_date` date,
`arrival_date` date,
`shipped_date` date,
`bl_number` varchar(140),
`discharged_date` date,
`mode_of_transport` varchar(140),
`clearing_company` varchar(140),
`bl_received` datetime(6),
`clearing_agent_bl` datetime(6),
`expected_clearing_completion_date` date,
`clearing_completion_date` date,
`date_of_invoice` date,
`date_received_registration_card` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:08,790 WARNING database DDL Query made to DB:
create table `tabPurchase And Stock Management Test` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`pstest` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:08,894 WARNING database DDL Query made to DB:
create table `tabOrder Tracking Container` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`co_name` varchar(140),
`co_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:09,140 WARNING database DDL Query made to DB:
create table `tabBin List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`current_label` varchar(140),
`new_label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:09,302 WARNING database DDL Query made to DB:
create table `tabMarketing Dept` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dept_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:09,397 WARNING database DDL Query made to DB:
create table `tabAllert Custom` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:09,545 WARNING database DDL Query made to DB:
create table `tabProducts of Interest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`extra_details` varchar(140),
`customer_item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`net_rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`base_rate` decimal(21,9) not null default 0,
`base_net_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`pricing_rule` varchar(140),
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`projected_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`prevdoc_doctype` varchar(140),
`prevdoc_docname` varchar(140),
`item_tax_rate` longtext,
`page_break` int(1) not null default 0,
`item_group` varchar(140),
`brand` varchar(140),
index `item_code`(`item_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:09,642 WARNING database DDL Query made to DB:
create table `tabCommunications` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type_of_communication` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`contacted_by` varchar(140),
`date_of_communication` datetime(6),
`communication_feedback` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:09,748 WARNING database DDL Query made to DB:
create table `tabPast Sales` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`item_sold` varchar(140),
`customer` varchar(140),
`amount` decimal(21,9) not null default 0,
`sold_date` date,
`serial_no` varchar(140),
`plate_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:09,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Item` ADD COLUMN `item_code` varchar(140), ADD COLUMN `serial_no` varchar(140), ADD COLUMN `reference` varchar(140)
2025-06-02 11:06:09,947 WARNING database DDL Query made to DB:
create table `tabPayment Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`planned_date` date,
`planned_amount` decimal(21,9) not null default 0,
`actual_date` date,
`actual_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:10,064 WARNING database DDL Query made to DB:
create table `tabPast Serial No` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`serial_no` varchar(140),
`item_code` varchar(140),
`past_item_code` varchar(140),
`customer` varchar(140),
`past_item_group` varchar(140),
`amount` decimal(21,9) not null default 0,
`sales_ref_no` varchar(140),
`date_of_sale` date,
`plate_number` varchar(140),
`extra_details` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:10,248 WARNING database DDL Query made to DB:
create table `tabIssued Items Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`requested` decimal(21,9) not null default 0,
`issued` decimal(21,9) not null default 0,
`difference` decimal(21,9) not null default 0,
`units` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:10,372 WARNING database DDL Query made to DB:
create table `tabRequested Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`approval_status` varchar(140) default 'Waiting Approval',
`items_issue_status` varchar(140) default 'Waiting Approval',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:10,499 WARNING database DDL Query made to DB:
create table `tabWorkshop Service Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:10,604 WARNING database DDL Query made to DB:
create table `tabUsed Items Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`item` varchar(140),
`item_description` varchar(140),
`quantity` decimal(21,9) not null default 0,
`units` varchar(140),
`extra_information` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:10,707 WARNING database DDL Query made to DB:
create table `tabRequested Items Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`item` varchar(140),
`description` longtext,
`quantity` decimal(21,9) not null default 0,
`units` varchar(140),
`requested_for` varchar(140),
`status` varchar(140) default 'Open',
`recommended_by` varchar(140),
`recommended_date` datetime(6),
`approved_by` varchar(140),
`approved_date` datetime(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:10,821 WARNING database DDL Query made to DB:
create table `tabWorkshop Services Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`work_done` text,
`service_status` varchar(140),
`subcontracted` int(1) not null default 0,
`subcontractor` varchar(140),
`technician` varchar(140),
`start_date` date,
`start_time` time(6),
`end_date` date,
`end_time` time(6),
`billable_hours` decimal(21,9) not null default 0,
`rate_per_hour` decimal(21,9) not null default 0,
`currency_rate_per_hour` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:10,911 WARNING database DDL Query made to DB:
create table `tabWorkshop Service` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`service_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:11,017 WARNING database DDL Query made to DB:
create table `tabWorkshop Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`requested_for` varchar(140),
`requested_for_docname` varchar(140),
`requested_date` date,
`request_type` varchar(140),
`previous_job` varchar(140),
`details` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:11,184 WARNING database DDL Query made to DB:
ALTER TABLE `tabOTP Register` ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `otp_secret` text, ADD COLUMN `registered` int(1) not null default 0, ADD COLUMN `amended_from` varchar(140)
2025-06-02 11:06:11,355 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric User Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:11,474 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric Device` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`device_id` varchar(140) unique,
`device_nick_name` varchar(140),
`installed_date` date,
`device_site_name` varchar(140),
`device_location` varchar(140),
`device_supervisor` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:11,582 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`punch` varchar(140),
`user_id` varchar(140),
`uid` varchar(140),
`status` varchar(140),
`timestamp` datetime(6),
`device_id` varchar(140),
`device_ip` varchar(140),
`punch_direction` varchar(140),
`biometric_user` varchar(140),
`biometric_user_name` varchar(140),
`biometric` varchar(140),
`meal_type` varchar(140),
`site_name` varchar(140),
`supervisor` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:11,703 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_id` varchar(140) unique,
`uid` varchar(140),
`erpnext_user` varchar(140),
`user_name` varchar(140),
`user_type` varchar(140),
`amended_from` varchar(140),
`gender` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:11,849 WARNING database DDL Query made to DB:
create table `tabCSF TZ Meal Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meal_name` varchar(140) unique,
`meal_type` varchar(140),
`start_time` time(6),
`end_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:11,985 WARNING database DDL Query made to DB:
create table `tabStanbic Setting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`enabled` int(1) not null default 1,
`currency` varchar(140),
`sftp_url` varchar(140),
`sftp_user` varchar(140),
`private_key` text,
`port` int(11) not null default 0,
`pgp_public_key` text,
`pgp_private_key` text,
`initiating_party_name` varchar(140),
`customerid` varchar(140),
`payment_type` varchar(140),
`user` varchar(140),
`ordering_customer_account_number` varchar(140),
`ordering_account_type` varchar(140),
`ordering_account_currency` varchar(140),
`ordering_bank_bic` varchar(140),
`ordering_bank_sort_code` varchar(140),
`ordering_bank_country` varchar(140),
`ordering_bank_country_code` varchar(140),
`charges_bearer` varchar(140),
`file_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:12,107 WARNING database DDL Query made to DB:
create table `tabStanbic Payments Info` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_slip` varchar(140),
`employee` varchar(140),
`transfer_currency` varchar(140),
`transfer_amount` decimal(21,9) not null default 0,
`beneficiary_bank_bic` varchar(140),
`beneficiary_bank_sort_code` varchar(140),
`beneficiary_bank_name` varchar(140),
`beneficiary_bank_country_code` varchar(140),
`beneficiary_name` varchar(140),
`beneficiary_country` varchar(140),
`beneficiary_address` varchar(140),
`beneficiary_iban` varchar(140),
`beneficiary_account_number` varchar(140),
`beneficiary_account_type` int(11) not null default 0,
`beneficiary_account_currency` varchar(140),
`stanbic_finaud_status` varchar(140),
`stanbic_intaud_status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:12,233 WARNING database DDL Query made to DB:
create table `tabStanbic Payments Initiation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_entry` varchar(140),
`file_code` varchar(140),
`amended_from` varchar(140),
`posting_date` date,
`posting_time` time(6),
`number_of_transactions` int(11) not null default 0,
`control_sum` decimal(21,9) not null default 0,
`stanbic_setting` varchar(140),
`initiating_party_name` varchar(140),
`customer_id` varchar(140),
`ordering_customer_name` varchar(140),
`ordering_customer_account_country` varchar(140) default 'TZ',
`ordering_customer_account_number` varchar(140),
`ordering_account_type` varchar(140),
`ordering_account_currency` varchar(140),
`ordering_bank_bic` varchar(140),
`ordering_bank_sort_code` varchar(140),
`ordering_bank_country_code` varchar(140),
`charges_bearer` varchar(140) default 'DEBT',
`xml` longtext,
`encrypted_xml` longtext,
`stanbic_ack` longtext,
`stanbic_intaud` longtext,
`stanbic_finaud` longtext,
`stanbic_ack_status` varchar(140),
`stanbic_ack_change` int(1) not null default 0,
`stanbic_intaud_change` int(1) not null default 0,
`stanbic_finaud_change` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:23,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD COLUMN `trip_destination` varchar(140), ADD COLUMN `destination_description` varchar(140)
2025-06-02 11:06:23,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 11:06:23,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` ADD COLUMN `stock_reconciliation` varchar(140)
2025-06-02 11:06:23,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0
2025-06-02 11:06:23,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `weight_per_unit` decimal(21,9) not null default 0, ADD COLUMN `total_weight` decimal(21,9) not null default 0, ADD COLUMN `weight_uom` varchar(140)
2025-06-02 11:06:23,884 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-02 11:06:23,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `electronic_fiscal_device` varchar(140)
2025-06-02 11:06:24,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-06-02 11:06:24,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustom DocPerm` ADD COLUMN `dependent` int(1) not null default 0
2025-06-02 11:06:24,137 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier` ADD COLUMN `vrn` varchar(140)
2025-06-02 11:06:24,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` ADD COLUMN `csf_tz_year` int(11) not null default 0, ADD COLUMN `csf_tz_acquisition_odometer` int(11) not null default 0, ADD COLUMN `csf_tz_engine_number` varchar(140)
2025-06-02 11:06:24,224 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `vehicle_value` decimal(21,9) not null default 0, MODIFY `uom` varchar(140)
2025-06-02 11:06:24,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` ADD COLUMN `import_file` varchar(140)
2025-06-02 11:06:24,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-02 11:06:24,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD COLUMN `material_request` varchar(140)
2025-06-02 11:06:24,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `current_valuation_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `current_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount_difference` decimal(21,9) not null default 0
2025-06-02 11:06:24,485 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `company_bank_details` text, ADD COLUMN `vrn` varchar(140), ADD COLUMN `tin` varchar(140), ADD COLUMN `p_o_box` varchar(140), ADD COLUMN `city` varchar(140), ADD COLUMN `plot_number` varchar(140), ADD COLUMN `block_number` varchar(140), ADD COLUMN `street` varchar(140), ADD COLUMN `max_records_in_dialog` int(11) not null default 0, ADD COLUMN `default_withholding_payable_account` varchar(140), ADD COLUMN `auto_create_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `default_withholding_receivable_account` varchar(140), ADD COLUMN `auto_create_for_sales_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_sales_withholding` int(1) not null default 0, ADD COLUMN `bypass_material_request_validation` int(1) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1
2025-06-02 11:06:24,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-06-02 11:06:24,611 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `posting_date` date, ADD COLUMN `default_item_discount` decimal(21,9) not null default 0
2025-06-02 11:06:24,633 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-02 11:06:24,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `previous_invoice_number` varchar(140), ADD COLUMN `default_item_discount` decimal(21,9) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `price_reduction` decimal(21,9) not null default 0, ADD COLUMN `tra_control_number` varchar(140), ADD COLUMN `witholding_tax_certificate_number` varchar(140), ADD COLUMN `electronic_fiscal_device` varchar(140), ADD COLUMN `efd_z_report` varchar(140), ADD COLUMN `excise_duty_applicable` int(1) not null default 0, ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1, ADD COLUMN `delivery_status` varchar(140)
2025-06-02 11:06:24,761 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0
2025-06-02 11:06:24,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `repack_template` varchar(140), ADD COLUMN `item_uom` varchar(140), ADD COLUMN `repack_qty` decimal(21,9) not null default 0, ADD COLUMN `final_destination` varchar(140), ADD COLUMN `total_net_weight` decimal(21,9) not null default 0, ADD COLUMN `transporter` varchar(140), ADD COLUMN `driver` varchar(140), ADD COLUMN `transport_receipt_no` varchar(140), ADD COLUMN `vehicle_no` varchar(140), ADD COLUMN `transporter_name` varchar(140), ADD COLUMN `driver_name` varchar(140), ADD COLUMN `transport_receipt_date` date
2025-06-02 11:06:24,856 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0
2025-06-02 11:06:24,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` ADD COLUMN `source_warehouse` varchar(140), ADD COLUMN `fg_warehouse` varchar(140), ADD COLUMN `wip_warehouse` varchar(140), ADD COLUMN `scrap_warehouse` varchar(140)
2025-06-02 11:06:24,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-06-02 11:06:25,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `vrn` varchar(140)
2025-06-02 11:06:25,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-06-02 11:06:25,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140)
2025-06-02 11:06:25,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:06:25,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` ADD COLUMN `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, ADD COLUMN `excisable_item` int(1) not null default 0, ADD COLUMN `default_tax_template` varchar(140)
2025-06-02 11:06:25,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0
2025-06-02 11:06:25,345 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `form_sales_invoice` varchar(140)
2025-06-02 11:06:25,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-06-02 11:06:25,426 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` ADD COLUMN `image` text
2025-06-02 11:06:25,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` MODIFY `total_operation_time` decimal(21,9) not null default 0
2025-06-02 11:06:25,549 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `is_ignored_in_pending_qty` int(1) not null default 0, ADD COLUMN `allow_over_sell` int(1) not null default 0, ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140), ADD COLUMN `allow_override_net_rate` int(1) not null default 0, ADD COLUMN `delivery_status` varchar(140)
